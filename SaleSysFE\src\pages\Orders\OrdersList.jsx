import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  DatePicker,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Dropdown,
  Menu,
  message,
  Modal,
  Tooltip,
  Alert,
} from "antd";
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  RollbackOutlined,
  UserOutlined,
  CalendarOutlined,
  ExportOutlined,
  FilterOutlined,
} from "@ant-design/icons";
import {
  useOrders,
  useUpdateOrderStatus,
  useDeleteOrder,
  useCreateOrderPayment,
  useReturnOrder,
} from "../../hooks/useOrders";
import {
  ORDER_STATUS,
  getOrderStatusInfo,
} from "../../constants/orderConstants";
import PaymentModal from "../../components/PaymentModal";
import dayjs from "dayjs";

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const OrdersList = () => {
  const navigate = useNavigate();

  // States
  const [searchText, setSearchText] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [dateRange, setDateRange] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [paymentModal, setPaymentModal] = useState({
    visible: false,
    orderData: null,
  });
  const [returnModal, setReturnModal] = useState({
    visible: false,
    orderData: null,
    lyDoHoanHang: "",
    ghiChuHoanHang: "",
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [bulkUpdateModal, setBulkUpdateModal] = useState({
    visible: false,
    newStatus: "",
  });

  // API hooks
  const { data: ordersData, isLoading } = useOrders({
    page: pagination.current,
    limit: pagination.pageSize,
    search: searchText,
    trang_thai: statusFilter,
    tu_ngay: dateRange[0]?.format("YYYY-MM-DD"),
    den_ngay: dateRange[1]?.format("YYYY-MM-DD"),
  });

  const updateStatusMutation = useUpdateOrderStatus();
  const deleteOrderMutation = useDeleteOrder();
  const createPaymentMutation = useCreateOrderPayment();
  const returnOrderMutation = useReturnOrder();

  const orders = ordersData?.data || [];
  const stats = ordersData?.stats || {};

  // Status options
  const statusOptions = [
    { value: "", label: "Tất cả trạng thái" },
    ...ORDER_STATUS,
  ];

  // Status options for actions menu (exclude only hoan_hang since it's handled separately)
  const actionStatusOptions = ORDER_STATUS.filter(
    (status) => !["hoan_hang"].includes(status.value)
  );

  // Get status config
  const getStatusConfig = (status) => {
    return (
      statusOptions.find((opt) => opt.value === status) || {
        color: "default",
        label: status,
      }
    );
  };

  // Handle status change
  const handleStatusChange = (orderId, newStatus) => {
    Modal.confirm({
      title: "Xác nhận thay đổi trạng thái",
      content: `Bạn có chắc chắn muốn thay đổi trạng thái đơn hàng này?`,
      onOk: () => {
        updateStatusMutation.mutate({ id: orderId, status: newStatus });
      },
    });
  };

  // Handle delete order
  const handleDeleteOrder = (orderId) => {
    Modal.confirm({
      title: "Xác nhận xóa đơn hàng",
      content:
        "Bạn có chắc chắn muốn xóa đơn hàng này? Hành động này không thể hoàn tác.",
      okText: "Xóa",
      okType: "danger",
      cancelText: "Hủy",
      onOk: () => {
        deleteOrderMutation.mutate(orderId);
      },
    });
  };

  // Handle payment
  const handlePayment = (orderData) => {
    // Kiểm tra điều kiện thanh toán
    if (!orderData.khach_hang_id || orderData.ten_khach_hang === "Khách lẻ") {
      message.warning("Không thể thanh toán cho đơn hàng khách lẻ");
      return;
    }

    const confirmedStatuses = [
      "da_xac_nhan",
      "da_dong_goi",
      "da_giao",
      "hoan_thanh",
    ];
    if (!confirmedStatuses.includes(orderData.trang_thai)) {
      message.warning("Chỉ có thể thanh toán cho đơn hàng đã được xác nhận");
      return;
    }

    // Kiểm tra thanh toán: tong_da_tra >= tong_phai_tra
    const totalAmount = orderData.tong_phai_tra || 0;
    const paidAmount = orderData.tong_da_tra || 0;

    if (paidAmount >= totalAmount) {
      message.info("Đơn hàng này đã được thanh toán đầy đủ");
      return;
    }

    setPaymentModal({
      visible: true,
      orderData: orderData,
    });
  };

  // Handle payment submit
  const handlePaymentSubmit = async (paymentData) => {
    try {
      await createPaymentMutation.mutateAsync(paymentData);
      setPaymentModal({ visible: false, orderData: null });
    } catch (error) {
      console.error("Payment error:", error);
    }
  };

  // Handle payment modal close
  const handlePaymentModalClose = () => {
    setPaymentModal({ visible: false, orderData: null });
  };

  // Handle return order
  const handleReturnOrder = (orderData) => {
    // Kiểm tra trạng thái có thể hoàn hàng (chỉ khi đã giao cho ĐVVC)
    if (orderData.trang_thai !== "da_giao") {
      message.warning("Chỉ có thể hoàn hàng đơn đã giao cho đơn vị vận chuyển");
      return;
    }

    setReturnModal({
      visible: true,
      orderData: orderData,
      lyDoHoanHang: "",
      ghiChuHoanHang: "",
    });
  };

  // Handle return submit
  const handleReturnSubmit = async () => {
    if (!returnModal.lyDoHoanHang.trim()) {
      message.warning("Vui lòng nhập lý do hoàn hàng");
      return;
    }

    try {
      await returnOrderMutation.mutateAsync({
        id: returnModal.orderData.id,
        returnData: {
          ly_do_hoan_hang: returnModal.lyDoHoanHang,
          ghi_chu_hoan_hang: returnModal.ghiChuHoanHang,
        },
      });
      setReturnModal({
        visible: false,
        orderData: null,
        lyDoHoanHang: "",
        ghiChuHoanHang: "",
      });
    } catch (error) {
      console.error("Return error:", error);
    }
  };

  // Handle return modal close
  const handleReturnModalClose = () => {
    setReturnModal({
      visible: false,
      orderData: null,
      lyDoHoanHang: "",
      ghiChuHoanHang: "",
    });
  };

  // Handle bulk update status
  const handleBulkUpdateStatus = async () => {
    if (!bulkUpdateModal.newStatus) {
      message.warning("Vui lòng chọn trạng thái mới");
      return;
    }

    if (selectedRowKeys.length === 0) {
      message.warning("Vui lòng chọn ít nhất một đơn hàng");
      return;
    }

    try {
      // Cập nhật từng đơn hàng
      const updatePromises = selectedRowKeys.map((orderId) =>
        updateStatusMutation.mutateAsync({
          id: orderId,
          status: bulkUpdateModal.newStatus,
        })
      );

      await Promise.all(updatePromises);

      message.success(
        `Đã cập nhật trạng thái cho ${selectedRowKeys.length} đơn hàng`
      );
      setBulkUpdateModal({ visible: false, newStatus: "" });
      setSelectedRowKeys([]);
    } catch (error) {
      console.error("Bulk update error:", error);
      message.error("Có lỗi xảy ra khi cập nhật trạng thái");
    }
  };

  // Table columns
  const columns = [
    {
      title: "Mã đơn hàng",
      dataIndex: "ma_don_hang",
      key: "ma_don_hang",
      width: 120,
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => navigate(`/orders/${record.id}`)}
          style={{ padding: 0, fontWeight: 500 }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: "Tên khách hàng",
      key: "customer",
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            {record.ten_khach_hang || "Khách lẻ"}
          </div>
          {record.so_dien_thoai && (
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.so_dien_thoai}
            </Text>
          )}
        </div>
      ),
    },
    // {
    //   title: 'Khách hàng',
    //   key: 'customer',
    //   width: 200,
    //   render: (_, record) => (
    //     <div>
    //       <div style={{ fontWeight: 500 }}>
    //         {record.ten_khach_hang || 'Khách lẻ'}
    //       </div>
    //       {record.so_dien_thoai && (
    //         <Text type="secondary" style={{ fontSize: 12 }}>
    //           {record.so_dien_thoai}
    //         </Text>
    //       )}
    //     </div>
    //   )
    // },
    {
      title: "Ngày đặt",
      dataIndex: "ngay_dat_hang",
      key: "ngay_dat_hang",
      width: 120,
      render: (date) => dayjs(date).format("DD/MM/YYYY"),
    },
    {
      title: "Sản phẩm",
      key: "products",
      width: 100,
      align: "center",
      render: (_, record) => <Text>{record.so_luong_san_pham || 0} SP</Text>,
    },
    {
      title: "Tổng tiền",
      dataIndex: "tong_tien",
      key: "tong_tien",
      width: 120,
      align: "right",
      render: (amount) => (
        <Text strong style={{ color: "#1890ff" }}>
          {amount?.toLocaleString("vi-VN")}đ
        </Text>
      ),
    },
    {
      title: "Tiền COD",
      dataIndex: "tien_cod",
      key: "tien_cod",
      width: 120,
      align: "right",
      render: (amount) => (
        <Text strong style={{ color: "#fa8c16" }}>
          {(amount || 0)?.toLocaleString("vi-VN")}đ
        </Text>
      ),
    },
    {
      title: "Thanh toán",
      key: "payment_status",
      width: 140,
      align: "center",
      render: (_, record) => {
        // Sử dụng tong_phai_tra để tính toán thanh toán
        const totalAmount = record.tong_phai_tra || 0;
        const paidAmount = record.tong_da_tra || 0;
        const remainingAmount = totalAmount - paidAmount;
        const confirmedStatuses = [
          "da_xac_nhan",
          "da_dong_goi",
          "da_giao",
          "hoan_thanh",
        ];
        const hasCustomer =
          record.khach_hang_id && record.ten_khach_hang !== "Khách lẻ";
        const canPay =
          hasCustomer &&
          confirmedStatuses.includes(record.trang_thai) &&
          remainingAmount > 0;

        // Debug payment status
        console.log(`💰 Payment debug for order ${record.ma_don_hang}:`, {
          tong_phai_tra: record.tong_phai_tra,
          tong_da_tra: record.tong_da_tra,
          remainingAmount,
          isFullyPaid: remainingAmount <= 0,
        });

        // Hiển thị trạng thái thanh toán
        return (
          <div>
            <div style={{ marginBottom: 4 }}>
              <Text strong style={{ fontSize: 14, color: "#1890ff" }}>
                {paidAmount.toLocaleString("vi-VN")}đ
              </Text>
              <div>
                <Text type="secondary" style={{ fontSize: 11 }}>
                  {remainingAmount <= 0
                    ? "Đã thanh toán đủ"
                    : `Còn: ${remainingAmount.toLocaleString("vi-VN")}đ`}
                </Text>
              </div>
            </div>
            {canPay ? (
              <Button
                type="primary"
                size="small"
                icon={<DollarOutlined />}
                onClick={() => handlePayment(record)}
              >
                Cập nhật thanh toán
              </Button>
            ) : remainingAmount <= 0 ? (
              <Tag color="green">Đã thanh toán</Tag>
            ) : !hasCustomer ? (
              <Tag color="default">Khách lẻ</Tag>
            ) : (
              <Tag color="orange">Chờ xác nhận</Tag>
            )}
          </div>
        );
      },
    },
    {
      title: "Trạng thái",
      dataIndex: "trang_thai",
      key: "trang_thai",
      width: 120,
      render: (status) => {
        const config = getStatusConfig(status);
        return <Tag color={config.color}>{config.label}</Tag>;
      },
    },
    {
      title: "Thao tác",
      key: "actions",
      width: 120,
      align: "center",
      render: (_, record) => {
        const remainingAmount = record.con_phai_tra || 0;
        const confirmedStatuses = [
          "da_xac_nhan",
          "da_dong_goi",
          "da_giao",
          "hoan_thanh",
        ];
        const hasCustomer =
          record.khach_hang_id && record.ten_khach_hang !== "Khách lẻ";
        const canPay =
          hasCustomer &&
          confirmedStatuses.includes(record.trang_thai) &&
          remainingAmount > 0;

        // Kiểm tra có thể hoàn hàng (chỉ khi đã giao cho ĐVVC)
        const canReturn = record.trang_thai === "da_giao";

        const menuItems = [
          {
            key: "view",
            icon: <EyeOutlined />,
            label: "Xem chi tiết",
            onClick: () => navigate(`/orders/${record.id}`),
          },
          {
            key: "edit",
            icon: <EditOutlined />,
            label: "Chỉnh sửa",
            onClick: () => navigate(`/orders/${record.id}/edit`),
          },
          ...(canPay
            ? [
                { type: "divider" },
                {
                  key: "payment",
                  icon: <DollarOutlined />,
                  label: "Cập nhật thanh toán",
                  onClick: () => handlePayment(record),
                },
              ]
            : []),
          ...(canReturn
            ? [
                { type: "divider" },
                {
                  key: "return",
                  icon: <RollbackOutlined />,
                  label: "Hoàn hàng",
                  onClick: () => handleReturnOrder(record),
                  danger: true,
                },
              ]
            : []),
          { type: "divider" },
          ...actionStatusOptions.map((status) => ({
            key: `status-${status.value}`,
            label: `Chuyển thành ${status.label}`,
            disabled: record.trang_thai === status.value,
            onClick: () => handleStatusChange(record.id, status.value),
          })),
          { type: "divider" },
          {
            key: "delete",
            icon: <DeleteOutlined />,
            label: "Xóa đơn hàng",
            danger: true,
            onClick: () => handleDeleteOrder(record.id),
          },
        ];

        return (
          <Space>
            <Tooltip title="Xem chi tiết">
              <Button
                type="text"
                icon={<EyeOutlined />}
                onClick={() => navigate(`/orders/${record.id}`)}
                size="small"
              />
            </Tooltip>
            <Dropdown
              menu={{ items: menuItems }}
              trigger={["click"]}
              placement="bottomRight"
            >
              <Button type="text" icon={<MoreOutlined />} size="small" />
            </Dropdown>
          </Space>
        );
      },
    },
  ];

  // Handle table change
  const handleTableChange = (newPagination) => {
    setPagination({
      ...pagination,
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    });
  };

  return (
    <div style={{ padding: "24px" }}>
      {/* Header */}
      <div
        style={{
          marginBottom: "24px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Title level={3} style={{ margin: 0 }}>
          <ShoppingCartOutlined style={{ marginRight: 8 }} />
          Quản lý đơn hàng
        </Title>
        <Space>
          <Button icon={<ExportOutlined />}>Xuất Excel</Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigate("/orders/create")}
          >
            Tạo đơn hàng
          </Button>
        </Space>
      </div>

      {/* Statistics */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng đơn hàng"
              value={stats.total_orders || 0}
              prefix={<ShoppingCartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Chờ xác nhận"
              value={stats.pending_orders || 0}
              valueStyle={{ color: "#faad14" }}
              prefix={<CalendarOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Đã hoàn thành"
              value={stats.completed_orders || 0}
              valueStyle={{ color: "#52c41a" }}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Doanh thu"
              value={stats.total_revenue || 0}
              formatter={(value) => `${value.toLocaleString("vi-VN")}đ`}
              valueStyle={{ color: "#1890ff" }}
              prefix={<DollarOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Input
              placeholder="Tìm theo mã đơn hàng, tên khách hàng, SĐT..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Trạng thái"
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: "100%" }}
              allowClear
            >
              {statusOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              placeholder={["Từ ngày", "Đến ngày"]}
              value={dateRange}
              onChange={setDateRange}
              style={{ width: "100%" }}
              format="DD/MM/YYYY"
            />
          </Col>
          <Col span={6}>
            <Space>
              <Button
                icon={<FilterOutlined />}
                onClick={() => {
                  setSearchText("");
                  setStatusFilter("");
                  setDateRange([]);
                }}
              >
                Xóa bộ lọc
              </Button>
              <Button
                type="primary"
                disabled={selectedRowKeys.length === 0}
                onClick={() =>
                  setBulkUpdateModal({ visible: true, newStatus: "" })
                }
              >
                Cập nhật trạng thái ({selectedRowKeys.length})
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Orders Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={isLoading}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
            getCheckboxProps: (record) => ({
              disabled: false,
              name: record.ma_don_hang,
            }),
          }}
          pagination={{
            ...pagination,
            total: ordersData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} đơn hàng`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* Payment Modal */}
      <PaymentModal
        visible={paymentModal.visible}
        orderData={paymentModal.orderData}
        onCancel={handlePaymentModalClose}
        onSubmit={handlePaymentSubmit}
        loading={createPaymentMutation.isLoading}
      />

      {/* Return Order Modal */}
      <Modal
        title="Hoàn hàng"
        open={returnModal.visible}
        onOk={handleReturnSubmit}
        onCancel={handleReturnModalClose}
        confirmLoading={returnOrderMutation.isLoading}
        okText="Xác nhận hoàn hàng"
        cancelText="Hủy"
        okButtonProps={{ danger: true }}
        width={600}
      >
        {returnModal.orderData && (
          <>
            <div style={{ marginBottom: "16px" }}>
              <Typography.Text strong>Đơn hàng: </Typography.Text>
              <Typography.Text>
                {returnModal.orderData.ma_don_hang}
              </Typography.Text>
            </div>
            <div style={{ marginBottom: "16px" }}>
              <Typography.Text strong>Khách hàng: </Typography.Text>
              <Typography.Text>
                {returnModal.orderData.ten_khach_hang}
              </Typography.Text>
            </div>
            <div style={{ marginBottom: "16px" }}>
              <Typography.Text strong>Trạng thái hiện tại: </Typography.Text>
              <Tag
                color={getStatusConfig(returnModal.orderData.trang_thai).color}
              >
                {getStatusConfig(returnModal.orderData.trang_thai).label}
              </Tag>
            </div>

            <div style={{ marginBottom: "16px" }}>
              <Typography.Text strong style={{ color: "red" }}>
                Lý do hoàn hàng *
              </Typography.Text>
              <Input
                placeholder="Nhập lý do hoàn hàng..."
                value={returnModal.lyDoHoanHang}
                onChange={(e) =>
                  setReturnModal((prev) => ({
                    ...prev,
                    lyDoHoanHang: e.target.value,
                  }))
                }
                style={{ marginTop: "8px" }}
              />
            </div>

            <div style={{ marginBottom: "16px" }}>
              <Typography.Text strong>Ghi chú thêm</Typography.Text>
              <Input.TextArea
                placeholder="Ghi chú thêm về việc hoàn hàng..."
                value={returnModal.ghiChuHoanHang}
                onChange={(e) =>
                  setReturnModal((prev) => ({
                    ...prev,
                    ghiChuHoanHang: e.target.value,
                  }))
                }
                rows={3}
                style={{ marginTop: "8px" }}
              />
            </div>

            <Alert
              message="Lưu ý"
              description="Hoàn hàng sẽ cập nhật lại tồn kho và điều chỉnh công nợ khách hàng. Thao tác này không thể hoàn tác."
              type="warning"
              showIcon
              style={{ marginTop: "16px" }}
            />
          </>
        )}
      </Modal>

      {/* Bulk Update Status Modal */}
      <Modal
        title="Cập nhật trạng thái hàng loạt"
        open={bulkUpdateModal.visible}
        onOk={handleBulkUpdateStatus}
        onCancel={() => setBulkUpdateModal({ visible: false, newStatus: "" })}
        confirmLoading={updateStatusMutation.isLoading}
      >
        <div style={{ marginBottom: "16px" }}>
          <Text>
            Bạn đang cập nhật trạng thái cho{" "}
            <strong>{selectedRowKeys.length}</strong> đơn hàng
          </Text>
        </div>

        <div>
          <Text strong>Chọn trạng thái mới:</Text>
          <Select
            placeholder="Chọn trạng thái"
            value={bulkUpdateModal.newStatus}
            onChange={(value) =>
              setBulkUpdateModal((prev) => ({ ...prev, newStatus: value }))
            }
            style={{ width: "100%", marginTop: "8px" }}
          >
            {ORDER_STATUS.map((status) => (
              <Option key={status.value} value={status.value}>
                <Tag color={getOrderStatusInfo(status.value).color}>
                  {status.label}
                </Tag>
              </Option>
            ))}
          </Select>
        </div>

        <Alert
          message="Lưu ý"
          description="Thao tác này sẽ cập nhật trạng thái cho tất cả đơn hàng đã chọn. Việc thay đổi trạng thái có thể ảnh hưởng đến tồn kho và công nợ."
          type="warning"
          showIcon
          style={{ marginTop: "16px" }}
        />
      </Modal>
    </div>
  );
};

export default OrdersList;
