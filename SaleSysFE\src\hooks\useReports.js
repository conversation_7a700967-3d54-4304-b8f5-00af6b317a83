import { useQuery } from "react-query";
import { testAPI } from "../services/api";
import dayjs from "dayjs";

// Hook để lấy báo c<PERSON>o tổng quan
export const useOverviewReport = (params = {}) => {
  const {
    startDate = dayjs().subtract(30, "days").format("YYYY-MM-DD"),
    endDate = dayjs().format("YYYY-MM-DD"),
    period = "day",
    ...filterParams
  } = params;

  // Tạo query key bao gồm tất cả tham số
  const queryKey = [
    "overview-report",
    startDate,
    endDate,
    period,
    filterParams,
  ];

  return useQuery(
    queryKey,
    () =>
      testAPI.getOverviewReport({
        startDate,
        endDate,
        period,
        ...filterParams,
      }),
    {
      staleTime: 5 * 60 * 1000, // 5 phút
      cacheTime: 10 * 60 * 1000, // 10 phút
      refetchOnWindowFocus: false,
      retry: 2,
    }
  );
};

// Hook để lấy báo cáo sản phẩm
export const useProductReport = (params = {}) => {
  const {
    startDate = dayjs().subtract(30, "days").format("YYYY-MM-DD"),
    endDate = dayjs().format("YYYY-MM-DD"),
    limit = 20,
    ...filterParams
  } = params;

  const queryKey = ["product-report", startDate, endDate, limit, filterParams];

  return useQuery(
    queryKey,
    () =>
      testAPI.getProductReport({ startDate, endDate, limit, ...filterParams }),
    {
      staleTime: 5 * 60 * 1000,
      cacheTime: 10 * 60 * 1000,
      refetchOnWindowFocus: false,
      retry: 2,
    }
  );
};

// Hook để lấy báo cáo khách hàng
export const useCustomerReport = (params = {}) => {
  const {
    startDate = dayjs().subtract(30, "days").format("YYYY-MM-DD"),
    endDate = dayjs().format("YYYY-MM-DD"),
    limit = 20,
    ...filterParams
  } = params;

  const queryKey = ["customer-report", startDate, endDate, limit, filterParams];

  return useQuery(
    queryKey,
    () =>
      testAPI.getCustomerReport({ startDate, endDate, limit, ...filterParams }),
    {
      staleTime: 5 * 60 * 1000,
      cacheTime: 10 * 60 * 1000,
      refetchOnWindowFocus: false,
      retry: 2,
    }
  );
};

// Hook để lấy báo cáo hoạt động kinh doanh
export const useBusinessActivityReport = (params = {}) => {
  const {
    startDate = dayjs().subtract(30, "days").format("YYYY-MM-DD"),
    endDate = dayjs().format("YYYY-MM-DD"),
    customerType = "all",
    limit = 50,
    ...filterParams
  } = params;

  const queryKey = [
    "business-activity-report",
    startDate,
    endDate,
    customerType,
    limit,
    filterParams,
  ];

  return useQuery(
    queryKey,
    () =>
      testAPI.getBusinessActivityReport({
        startDate,
        endDate,
        customerType,
        limit,
        ...filterParams,
      }),
    {
      staleTime: 5 * 60 * 1000,
      cacheTime: 10 * 60 * 1000,
      refetchOnWindowFocus: false,
      retry: 2,
    }
  );
};

// Hook để lấy báo cáo nhóm khách hàng
export const useCustomerGroupReport = (params = {}) => {
  const {
    startDate = dayjs().subtract(30, "days").format("YYYY-MM-DD"),
    endDate = dayjs().format("YYYY-MM-DD"),
    limit = 20,
    ...filterParams
  } = params;

  const queryKey = [
    "customer-group-report",
    startDate,
    endDate,
    limit,
    filterParams,
  ];

  return useQuery(
    queryKey,
    () =>
      testAPI.getCustomerGroupReport({
        startDate,
        endDate,
        limit,
        ...filterParams,
      }),
    {
      staleTime: 5 * 60 * 1000,
      cacheTime: 10 * 60 * 1000,
      refetchOnWindowFocus: false,
      retry: 2,
    }
  );
};

// Hook tổng hợp tất cả báo cáo
export const useReportsData = (params = {}) => {
  const overviewQuery = useOverviewReport(params);
  const productQuery = useProductReport(params);
  const customerQuery = useCustomerReport(params);
  const businessActivityQuery = useBusinessActivityReport(params);
  const customerGroupQuery = useCustomerGroupReport(params);

  return {
    overview: {
      data: overviewQuery.data?.data,
      isLoading: overviewQuery.isLoading,
      error: overviewQuery.error,
      refetch: overviewQuery.refetch,
    },
    products: {
      data: productQuery.data?.data,
      isLoading: productQuery.isLoading,
      error: productQuery.error,
      refetch: productQuery.refetch,
    },
    customers: {
      data: customerQuery.data?.data,
      isLoading: customerQuery.isLoading,
      error: customerQuery.error,
      refetch: customerQuery.refetch,
    },
    businessActivity: {
      data: businessActivityQuery.data?.data,
      isLoading: businessActivityQuery.isLoading,
      error: businessActivityQuery.error,
      refetch: businessActivityQuery.refetch,
    },
    customerGroups: {
      data: customerGroupQuery.data?.data,
      isLoading: customerGroupQuery.isLoading,
      error: customerGroupQuery.error,
      refetch: customerGroupQuery.refetch,
    },
    // Trạng thái tổng hợp
    isLoading:
      overviewQuery.isLoading ||
      productQuery.isLoading ||
      customerQuery.isLoading ||
      businessActivityQuery.isLoading ||
      customerGroupQuery.isLoading,
    hasError:
      overviewQuery.error ||
      productQuery.error ||
      customerQuery.error ||
      businessActivityQuery.error ||
      customerGroupQuery.error,
    refetchAll: () => {
      overviewQuery.refetch();
      productQuery.refetch();
      customerQuery.refetch();
      businessActivityQuery.refetch();
      customerGroupQuery.refetch();
    },
  };
};
