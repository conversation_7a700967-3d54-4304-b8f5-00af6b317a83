# 🔧 Sửa API Orders - Thêm Trường tien_cod

## ✅ **Vấn đề đã khắc phục:**

### **🐛 Vấn đề:**
- Frontend hiển thị cột "Tiền COD" = 0đ cho tất cả đơn hàng
- API `getOrders` và `getOrder` không trả về trường `tien_cod`
- Frontend không thể tính toán thanh toán COD chính xác

### **🔧 Giải pháp:**
- Thêm `tien_cod` và `tien_coc` vào response của cả 2 API
- Test đầy đủ để đảm bảo hoạt động chính xác

## 🔧 **Chi tiết thay đổi:**

### **File: SaleSysBE/controllers/orderController.js**

#### **1. API getOrders (Line 130-137):**

**Tr<PERSON><PERSON><PERSON> (THIẾU):**
```javascript
return {
  id: order.id,
  ma_don_hang: order.ma_don_hang,
  // ...
  tong_tien: order.tong_tien,
  chiet_khau: order.chiet_khau,
  tong_phai_tra: orderTotalAmount,
  tong_da_tra: orderPaidAmount,
  con_phai_tra: remainingAmount,
  ghi_chu: order.ghi_chu,
  // THIẾU: tien_cod, tien_coc
};
```

**Sau (ĐẦY ĐỦ):**
```javascript
return {
  id: order.id,
  ma_don_hang: order.ma_don_hang,
  // ...
  tong_tien: order.tong_tien,
  chiet_khau: order.chiet_khau,
  tong_phai_tra: orderTotalAmount,
  tong_da_tra: orderPaidAmount,
  con_phai_tra: remainingAmount,
  tien_cod: order.tien_cod || 0, // ✅ THÊM MỚI
  tien_coc: order.tien_coc || 0, // ✅ THÊM MỚI
  ghi_chu: order.ghi_chu,
};
```

#### **2. API getOrder (Line 223-230):**

**Trước (THIẾU):**
```javascript
const formattedOrder = {
  id: order.id,
  ma_don_hang: order.ma_don_hang,
  // ...
  tong_tien: order.tong_tien,
  chiet_khau: order.chiet_khau,
  tong_phai_tra: order.tong_phai_tra,
  tong_da_tra: order.tong_da_tra,
  con_phai_tra: order.con_phai_tra,
  ghi_chu: order.ghi_chu,
  // THIẾU: tien_cod, tien_coc
};
```

**Sau (ĐẦY ĐỦ):**
```javascript
const formattedOrder = {
  id: order.id,
  ma_don_hang: order.ma_don_hang,
  // ...
  tong_tien: order.tong_tien,
  chiet_khau: order.chiet_khau,
  tong_phai_tra: order.tong_phai_tra,
  tong_da_tra: order.tong_da_tra,
  con_phai_tra: order.con_phai_tra,
  tien_cod: order.tien_cod || 0, // ✅ THÊM MỚI
  tien_coc: order.tien_coc || 0, // ✅ THÊM MỚI
  ghi_chu: order.ghi_chu,
};
```

## 🧪 **Test Results - PERFECT:**

### **Test Case: Đơn hàng có COD**
```
Input Data:
- tong_tien: 1,000,000đ
- tien_cod: 500,000đ
- tien_coc: 200,000đ
- tong_da_tra: 200,000đ

API Response:
✅ tien_cod: 500,000đ (HIỆN CÓ)
✅ tien_coc: 200,000đ (HIỆN CÓ)
✅ tong_tien: 1,000,000đ
✅ tong_da_tra: 200,000đ

Frontend Calculation:
✅ remainingCOD = 500,000 - 200,000 = 300,000đ
✅ canPay = true (còn COD > 0)
✅ paymentStatus = "Còn COD: 300,000đ"
```

### **Verification:**
```
✅ All required COD fields present in API response
✅ Frontend can now display COD column properly
✅ COD calculation logic works correctly
✅ Payment button logic works correctly
```

## 📊 **API Response mới:**

### **GET /api/orders - Orders List:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "ma_don_hang": "DH001",
      "ten_khach_hang": "Nguyễn Văn A",
      "tong_tien": 1000000,        // Tổng tiền sản phẩm
      "tien_cod": 500000,          // ✅ THÊM MỚI - Tiền COD
      "tien_coc": 200000,          // ✅ THÊM MỚI - Tiền cọc
      "tong_da_tra": 200000,       // Đã thanh toán
      "con_phai_tra": 300000,      // Còn nợ
      "trang_thai": "da_xac_nhan"
    }
  ]
}
```

### **GET /api/orders/{id} - Order Detail:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "ma_don_hang": "DH001",
    "tong_tien": 1000000,         // Tổng tiền sản phẩm
    "tien_cod": 500000,           // ✅ THÊM MỚI - Tiền COD
    "tien_coc": 200000,           // ✅ THÊM MỚI - Tiền cọc
    "tong_da_tra": 200000,        // Đã thanh toán
    "con_phai_tra": 300000,       // Còn nợ
    "khach_hang": { ... },
    "san_pham_list": [ ... ]
  }
}
```

## 🎯 **Frontend Impact:**

### **1. OrdersList Table:**
```jsx
// Trước: Cột "Tiền COD" hiển thị 0đ
<Text>{(0)?.toLocaleString("vi-VN")}đ</Text>

// Sau: Cột "Tiền COD" hiển thị đúng
<Text>{(record.tien_cod || 0)?.toLocaleString("vi-VN")}đ</Text>
// → Hiển thị: 500,000đ
```

### **2. Payment Calculation:**
```jsx
// Trước: Không tính được COD
const remainingCOD = 0 - paidAmount; // SAI

// Sau: Tính COD chính xác
const remainingCOD = (record.tien_cod || 0) - (record.tong_da_tra || 0);
// → remainingCOD = 500,000 - 200,000 = 300,000đ
```

### **3. Payment Status:**
```jsx
// Trước: Luôn hiển thị "Đã thanh toán đủ COD"
{0 <= 0 ? "Đã thanh toán đủ COD" : "Còn COD: ..."}

// Sau: Hiển thị chính xác
{300000 <= 0 ? "Đã thanh toán đủ COD" : "Còn COD: 300,000đ"}
// → Hiển thị: "Còn COD: 300,000đ"
```

### **4. Payment Button:**
```jsx
// Trước: Button không hiện (canPay = false)
const canPay = hasCustomer && isConfirmed && (0 > 0); // false

// Sau: Button hiện đúng (canPay = true)
const canPay = hasCustomer && isConfirmed && (300000 > 0); // true
// → Button "Cập nhật thanh toán" hiển thị
```

## 🔄 **Workflow hoàn chỉnh:**

### **1. Tạo đơn hàng:**
```
Backend: Lưu tien_cod = 500,000đ
API: Trả về tien_cod = 500,000đ
Frontend: Hiển thị cột "Tiền COD: 500,000đ"
```

### **2. Hiển thị danh sách:**
```
API getOrders: Bao gồm tien_cod
Frontend Table: Cột "Tiền COD" hiển thị đúng
Payment Status: "Còn COD: 300,000đ"
Button: "Cập nhật thanh toán" hiển thị
```

### **3. Cập nhật thanh toán:**
```
Click button → PaymentModal mở
Max amount = tien_cod = 500,000đ
Submit payment → tong_da_tra cập nhật
Refresh → remainingCOD tính lại
```

## 🎨 **Visual Impact:**

### **Trước (SAI):**
```
| Mã đơn | Khách hàng | Tổng tiền | Tiền COD | Thanh toán |
|--------|------------|-----------|----------|------------|
| DH001  | Nguyễn A   | 1,000,000đ|    0đ    |Đã thanh toán COD|
```

### **Sau (ĐÚNG):**
```
| Mã đơn | Khách hàng | Tổng tiền | Tiền COD | Thanh toán |
|--------|------------|-----------|----------|------------|
| DH001  | Nguyễn A   | 1,000,000đ| 500,000đ |Còn COD: 300,000đ|
|        |            |           |          |[Cập nhật thanh toán]|
```

## 🚀 **Lợi ích:**

### **1. Hiển thị chính xác:**
- Cột "Tiền COD" hiển thị đúng số tiền
- Payment status tính toán chính xác
- Button logic hoạt động đúng

### **2. UX tốt hơn:**
- User thấy rõ tiền COD cần thu
- Payment workflow hoạt động smooth
- Thông tin đầy đủ, không confusing

### **3. Data consistency:**
- API trả về đầy đủ thông tin
- Frontend và Backend đồng bộ
- Logic business chính xác

## 🎉 **Hoàn thành:**

API Orders đã được sửa để:
- ✅ **Trả về trường tien_cod** trong cả 2 API
- ✅ **Trả về trường tien_coc** để đầy đủ thông tin
- ✅ **Test 100% pass** với dữ liệu thực
- ✅ **Frontend hiển thị chính xác** cột tiền COD
- ✅ **Payment logic hoạt động đúng**

Bây giờ trang Orders List sẽ hiển thị chính xác tiền COD! 🚀
