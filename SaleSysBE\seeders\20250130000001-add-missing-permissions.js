"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Danh sách các quyền còn thiếu
    const missingPermissions = [
      // Quyền loại sản phẩm (categories)
      {
        ma_quyen: "XEM_LOAI_SAN_PHAM",
        ten_quyen: "Xem loại sản phẩm",
        mo_ta: "Quyền xem danh sách loại sản phẩm",
      },
      {
        ma_quyen: "THEM_LOAI_SAN_PHAM",
        ten_quyen: "Thêm loại sản phẩm",
        mo_ta: "Quyền tạo loại sản phẩm mới",
      },
      {
        ma_quyen: "SUA_LOAI_SAN_PHAM",
        ten_quyen: "Sửa loại sản phẩm",
        mo_ta: "Quyền chỉnh sửa loại sản phẩm",
      },
      {
        ma_quyen: "XOA_LOAI_SAN_PHAM",
        ten_quyen: "Xóa loại sản phẩm",
        mo_ta: "Quyền xóa loại sản phẩm",
      },

      // Quyền nhãn hiệu (brands)
      {
        ma_quyen: "XEM_NHAN_HIEU",
        ten_quyen: "Xem nhãn hiệu",
        mo_ta: "Quyền xem danh sách nhãn hiệu",
      },
      {
        ma_quyen: "THEM_NHAN_HIEU",
        ten_quyen: "Thêm nhãn hiệu",
        mo_ta: "Quyền tạo nhãn hiệu mới",
      },
      {
        ma_quyen: "SUA_NHAN_HIEU",
        ten_quyen: "Sửa nhãn hiệu",
        mo_ta: "Quyền chỉnh sửa nhãn hiệu",
      },
      {
        ma_quyen: "XOA_NHAN_HIEU",
        ten_quyen: "Xóa nhãn hiệu",
        mo_ta: "Quyền xóa nhãn hiệu",
      },

      // Quyền nhóm khách hàng
      {
        ma_quyen: "XEM_NHOM_KHACH_HANG",
        ten_quyen: "Xem nhóm khách hàng",
        mo_ta: "Quyền xem danh sách nhóm khách hàng",
      },
      {
        ma_quyen: "THEM_NHOM_KHACH_HANG",
        ten_quyen: "Thêm nhóm khách hàng",
        mo_ta: "Quyền tạo nhóm khách hàng mới",
      },
      {
        ma_quyen: "SUA_NHOM_KHACH_HANG",
        ten_quyen: "Sửa nhóm khách hàng",
        mo_ta: "Quyền chỉnh sửa nhóm khách hàng",
      },
      {
        ma_quyen: "XOA_NHOM_KHACH_HANG",
        ten_quyen: "Xóa nhóm khách hàng",
        mo_ta: "Quyền xóa nhóm khách hàng",
      },

      // Quyền tags
      {
        ma_quyen: "XEM_TAG",
        ten_quyen: "Xem tag",
        mo_ta: "Quyền xem danh sách tag",
      },
      {
        ma_quyen: "THEM_TAG",
        ten_quyen: "Thêm tag",
        mo_ta: "Quyền tạo tag mới",
      },
      {
        ma_quyen: "SUA_TAG",
        ten_quyen: "Sửa tag",
        mo_ta: "Quyền chỉnh sửa tag",
      },
      { ma_quyen: "XOA_TAG", ten_quyen: "Xóa tag", mo_ta: "Quyền xóa tag" },

      // Quyền kiểm kê (nếu chưa có)
      {
        ma_quyen: "XEM_KIEM_KE",
        ten_quyen: "Xem kiểm kê",
        mo_ta: "Quyền xem danh sách phiếu kiểm kê",
      },
      {
        ma_quyen: "THEM_KIEM_KE",
        ten_quyen: "Thêm kiểm kê",
        mo_ta: "Quyền tạo phiếu kiểm kê mới",
      },
      {
        ma_quyen: "SUA_KIEM_KE",
        ten_quyen: "Sửa kiểm kê",
        mo_ta: "Quyền chỉnh sửa phiếu kiểm kê",
      },
      {
        ma_quyen: "XOA_KIEM_KE",
        ten_quyen: "Xóa kiểm kê",
        mo_ta: "Quyền xóa phiếu kiểm kê",
      },

      // Quyền xuất dữ liệu
      {
        ma_quyen: "XUAT_DON_HANG",
        ten_quyen: "Xuất đơn hàng",
        mo_ta: "Quyền xuất danh sách đơn hàng ra Excel",
      },
      {
        ma_quyen: "XUAT_KHACH_HANG",
        ten_quyen: "Xuất khách hàng",
        mo_ta: "Quyền xuất danh sách khách hàng ra Excel",
      },
      {
        ma_quyen: "XUAT_SAN_PHAM",
        ten_quyen: "Xuất sản phẩm",
        mo_ta: "Quyền xuất danh sách sản phẩm ra Excel",
      },

      // Quyền nhập dữ liệu
      {
        ma_quyen: "NHAP_KHACH_HANG",
        ten_quyen: "Nhập khách hàng",
        mo_ta: "Quyền nhập danh sách khách hàng từ Excel",
      },
      {
        ma_quyen: "NHAP_SAN_PHAM",
        ten_quyen: "Nhập sản phẩm",
        mo_ta: "Quyền nhập danh sách sản phẩm từ Excel",
      },
    ];

    console.log("🔐 Adding missing permissions...");

    // Thêm từng quyền nếu chưa tồn tại
    for (const permission of missingPermissions) {
      const existingPermission = await queryInterface.sequelize.query(
        `SELECT id FROM quyen WHERE ma_quyen = '${permission.ma_quyen}'`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (existingPermission.length === 0) {
        await queryInterface.bulkInsert("quyen", [permission], {});
        console.log(`✅ Added permission: ${permission.ma_quyen}`);
      } else {
        console.log(`⚠️ Permission already exists: ${permission.ma_quyen}`);
      }
    }

    // Gán tất cả quyền mới cho role ADMIN
    const adminRole = await queryInterface.sequelize.query(
      `SELECT id FROM vai_tro WHERE ma_vai_tro = 'ADMIN'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (adminRole.length > 0) {
      const adminRoleId = adminRole[0].id;

      for (const permission of missingPermissions) {
        const permissionRecord = await queryInterface.sequelize.query(
          `SELECT id FROM quyen WHERE ma_quyen = '${permission.ma_quyen}'`,
          { type: Sequelize.QueryTypes.SELECT }
        );

        if (permissionRecord.length > 0) {
          const permissionId = permissionRecord[0].id;

          // Kiểm tra xem role-permission đã tồn tại chưa
          const existingRolePermission = await queryInterface.sequelize.query(
            `SELECT * FROM vai_tro_quyen WHERE vai_tro_id = ${adminRoleId} AND quyen_id = ${permissionId}`,
            { type: Sequelize.QueryTypes.SELECT }
          );

          if (existingRolePermission.length === 0) {
            await queryInterface.bulkInsert(
              "vai_tro_quyen",
              [
                {
                  vai_tro_id: adminRoleId,
                  quyen_id: permissionId,
                },
              ],
              {}
            );
            console.log(
              `✅ Assigned permission ${permission.ma_quyen} to ADMIN role`
            );
          }
        }
      }
    }

    // Gán quyền phù hợp cho role QUAN_LY
    const managerRole = await queryInterface.sequelize.query(
      `SELECT id FROM vai_tro WHERE ma_vai_tro = 'QUAN_LY'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (managerRole.length > 0) {
      const managerRoleId = managerRole[0].id;
      // QUAN_LY có hầu hết quyền trừ XOA
      const managerPermissions = missingPermissions.filter(
        (p) => !p.ma_quyen.includes("XOA_")
      );

      for (const permission of managerPermissions) {
        const permissionRecord = await queryInterface.sequelize.query(
          `SELECT id FROM quyen WHERE ma_quyen = '${permission.ma_quyen}'`,
          { type: Sequelize.QueryTypes.SELECT }
        );

        if (permissionRecord.length > 0) {
          const permissionId = permissionRecord[0].id;

          const existingRolePermission = await queryInterface.sequelize.query(
            `SELECT * FROM vai_tro_quyen WHERE vai_tro_id = ${managerRoleId} AND quyen_id = ${permissionId}`,
            { type: Sequelize.QueryTypes.SELECT }
          );

          if (existingRolePermission.length === 0) {
            await queryInterface.bulkInsert(
              "vai_tro_quyen",
              [
                {
                  vai_tro_id: managerRoleId,
                  quyen_id: permissionId,
                },
              ],
              {}
            );
            console.log(
              `✅ Assigned permission ${permission.ma_quyen} to QUAN_LY role`
            );
          }
        }
      }
    }

    console.log("🎉 Missing permissions added successfully!");
  },

  async down(queryInterface, Sequelize) {
    // Xóa các quyền đã thêm
    const permissionsToDelete = [
      "XEM_LOAI_SAN_PHAM",
      "THEM_LOAI_SAN_PHAM",
      "SUA_LOAI_SAN_PHAM",
      "XOA_LOAI_SAN_PHAM",
      "XEM_NHAN_HIEU",
      "THEM_NHAN_HIEU",
      "SUA_NHAN_HIEU",
      "XOA_NHAN_HIEU",
      "XEM_NHOM_KHACH_HANG",
      "THEM_NHOM_KHACH_HANG",
      "SUA_NHOM_KHACH_HANG",
      "XOA_NHOM_KHACH_HANG",
      "XEM_TAG",
      "THEM_TAG",
      "SUA_TAG",
      "XOA_TAG",
      "XEM_KIEM_KE",
      "THEM_KIEM_KE",
      "SUA_KIEM_KE",
      "XOA_KIEM_KE",
      "XUAT_DON_HANG",
      "XUAT_KHACH_HANG",
      "XUAT_SAN_PHAM",
      "NHAP_KHACH_HANG",
      "NHAP_SAN_PHAM",
    ];

    await queryInterface.bulkDelete(
      "quyen",
      {
        ma_quyen: permissionsToDelete,
      },
      {}
    );
  },
};
