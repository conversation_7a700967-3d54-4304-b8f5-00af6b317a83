# 🎯 Sửa Logic Quản Lý Tồn Kho

## ✅ **Vấn đề đã phát hiện và khắc phục:**

### **🐛 Vấn đề ban đầu:**
1. **Tạo đơn hàng:** Tồn kho không giảm khi tạo đơn hàng
2. **Cập nhật trạng thái:** Không có logic cập nhật tồn kho khi thay đổi trạng thái
3. **Hủy/Hoàn hàng:** Tồn kho không được cộng lại

### **🔧 Nguyên nhân:**
- `createOrder`: Chỉ trừ tồn kho khi trạng thái = `da_xac_nhan` hoặc `hoan_thanh`
- `updateOrderStatus`: **THIẾU LOGIC** cập nhật tồn kho
- Đơn hàng mới tạo có trạng thái `cho_xu_ly` → không trừ tồn kho

## 🔧 **Chi tiết sửa chữa:**

### **File: SaleSysBE/controllers/orderController.js**

#### **1. Thêm logic tồn kho trong updateOrderStatus (Line 811-849):**

**Trước (THIẾU LOGIC):**
```javascript
// Cập nhật trạng thái đơn hàng
await order.update({
  trang_thai: newStatus,
  nguoi_cap_nhap: req.user?.username || "system",
  ngay_cap_nhap: new Date(),
}, { transaction });
```

**Sau (CÓ LOGIC TỒN KHO):**
```javascript
// 🏭 XỬ LÝ TỒN KHO KHI THAY ĐỔI TRẠNG THÁI
const inventoryStatuses = ["da_xac_nhan", "da_dong_goi", "da_giao", "hoan_thanh"];
const shouldReduceInventory = inventoryStatuses.includes(newStatus);
const hadReducedInventory = inventoryStatuses.includes(oldStatus);

// Lấy chi tiết đơn hàng để cập nhật tồn kho
const orderDetails = await DonHangSanPham.findAll({
  where: { don_hang_id: id },
  transaction
});

if (orderDetails && orderDetails.length > 0) {
  if (!hadReducedInventory && shouldReduceInventory) {
    // Trừ tồn kho khi chuyển từ draft -> confirmed
    console.log(`🏭 Reducing inventory: ${oldStatus} -> ${newStatus}`);
    for (const item of orderDetails) {
      await updateInventoryForOrder(
        item.phien_ban_san_pham_id,
        -item.so_luong, // Trừ tồn kho
        transaction
      );
    }
  } else if (hadReducedInventory && !shouldReduceInventory) {
    // Cộng lại tồn kho khi hủy đơn
    console.log(`🏭 Restoring inventory: ${oldStatus} -> ${newStatus}`);
    for (const item of orderDetails) {
      await updateInventoryForOrder(
        item.phien_ban_san_pham_id,
        item.so_luong, // Cộng lại tồn kho
        transaction
      );
    }
  }
}

// Cập nhật trạng thái đơn hàng
await order.update({
  trang_thai: newStatus,
  nguoi_cap_nhap: req.user?.username || "system",
  ngay_cap_nhap: new Date(),
}, { transaction });
```

#### **2. Logic tồn kho đã có sẵn:**

**updateInventoryForOrder function (Line 661-675):**
```javascript
const updateInventoryForOrder = async (variantId, quantity, transaction) => {
  // Cập nhật tồn kho cho phiên bản sản phẩm
  const inventory = await TonKhoPhienBan.findOne({
    where: { phien_ban_san_pham_id: variantId },
  });

  if (inventory) {
    await inventory.update(
      {
        so_luong_ton: inventory.so_luong_ton + quantity,
      },
      { transaction }
    );
  }
};
```

**returnOrder function (Line 955-1090):**
```javascript
// Logic hoàn hàng đã có sẵn và hoạt động đúng
// Cộng lại tồn kho khi hoàn hàng
for (const orderProduct of order.sanPhamList) {
  const newTonKho = (tonKho.so_luong_ton || 0) + soLuongHoan;
  await tonKho.update({
    so_luong_ton: newTonKho,
    so_luong_co_the_ban: newCoTheBan,
  }, { transaction });
}
```

## 📊 **Logic mới hoàn chỉnh:**

### **1. Trạng thái ảnh hưởng tồn kho:**
```javascript
const inventoryStatuses = ["da_xac_nhan", "da_dong_goi", "da_giao", "hoan_thanh"];
```

### **2. Workflow tồn kho:**

#### **Tạo đơn hàng:**
```
Status: cho_xu_ly (draft)
→ Tồn kho: KHÔNG THAY ĐỔI ✅
```

#### **Xác nhận đơn hàng:**
```
cho_xu_ly → da_xac_nhan
→ Tồn kho: TRỪ số lượng đặt hàng ✅
```

#### **Hủy đơn hàng:**
```
da_xac_nhan → huy
→ Tồn kho: CỘNG LẠI số lượng đã trừ ✅
```

#### **Hoàn hàng:**
```
da_giao → hoan_hang
→ Tồn kho: CỘNG LẠI số lượng đã giao ✅
```

### **3. Các trường hợp cụ thể:**

#### **Case 1: Tạo đơn hàng mới**
```
Initial inventory: 100 units
Create order: 5 units (status: cho_xu_ly)
→ Inventory: 100 units (no change) ✅
```

#### **Case 2: Xác nhận đơn hàng**
```
Order status: cho_xu_ly → da_xac_nhan
→ Inventory: 100 - 5 = 95 units ✅
```

#### **Case 3: Hủy đơn hàng**
```
Order status: da_xac_nhan → huy
→ Inventory: 95 + 5 = 100 units ✅
```

#### **Case 4: Hoàn hàng**
```
Order status: da_giao → hoan_hang
→ Inventory: 95 + 5 = 100 units ✅
```

## 🎯 **Điều kiện logic:**

### **Trừ tồn kho khi:**
```javascript
if (!hadReducedInventory && shouldReduceInventory) {
  // Chuyển từ trạng thái KHÔNG ảnh hưởng → CÓ ảnh hưởng tồn kho
  // VD: cho_xu_ly → da_xac_nhan
}
```

### **Cộng lại tồn kho khi:**
```javascript
if (hadReducedInventory && !shouldReduceInventory) {
  // Chuyển từ trạng thái CÓ ảnh hưởng → KHÔNG ảnh hưởng tồn kho
  // VD: da_xac_nhan → huy
}
```

### **Không thay đổi tồn kho khi:**
```javascript
// Cả 2 trạng thái đều ảnh hưởng hoặc đều không ảnh hưởng
// VD: da_xac_nhan → da_dong_goi (cả 2 đều trừ tồn kho)
// VD: cho_xu_ly → huy (cả 2 đều không ảnh hưởng)
```

## 🚀 **Lợi ích:**

### **1. Logic chính xác:**
- ✅ **Tạo đơn:** Không trừ tồn kho ngay lập tức
- ✅ **Xác nhận:** Trừ tồn kho khi commit đơn hàng
- ✅ **Hủy/Hoàn:** Cộng lại tồn kho đúng cách

### **2. Workflow hợp lý:**
- ✅ **Draft orders:** Không ảnh hưởng tồn kho (có thể hủy dễ dàng)
- ✅ **Confirmed orders:** Commit tồn kho (đã xác nhận bán)
- ✅ **Cancelled/Returned:** Restore tồn kho (hàng trả lại)

### **3. Tính nhất quán:**
- ✅ **Mọi thay đổi trạng thái** đều trigger logic tồn kho
- ✅ **Transaction safety** đảm bảo data consistency
- ✅ **Logging** để debug và audit

## 🎨 **Frontend Impact:**

### **Tồn kho sẽ cập nhật đúng:**
```
Tạo đơn hàng:
- Frontend: Hiển thị đơn hàng mới
- Backend: Tồn kho không đổi ✅

Xác nhận đơn hàng:
- Frontend: Trạng thái "Đã xác nhận"
- Backend: Tồn kho giảm ✅

Hủy đơn hàng:
- Frontend: Trạng thái "Đã hủy"
- Backend: Tồn kho tăng lại ✅
```

### **Inventory page sẽ hiển thị:**
```
Product A: 100 units → 95 units → 100 units
(Tạo đơn)    (Xác nhận)    (Hủy đơn)
```

## 🔄 **API Endpoints ảnh hưởng:**

### **1. POST /api/orders (createOrder):**
- ✅ **Đã có logic** trừ tồn kho cho trạng thái confirmed
- ✅ **Hoạt động đúng** với workflow mới

### **2. PUT /api/orders/:id/status (updateOrderStatus):**
- ✅ **Đã thêm logic** cập nhật tồn kho
- ✅ **Xử lý tất cả** thay đổi trạng thái

### **3. POST /api/orders/:id/return (returnOrder):**
- ✅ **Đã có logic** hoàn tồn kho
- ✅ **Hoạt động đúng** từ trước

## 🎉 **Hoàn thành:**

Logic quản lý tồn kho đã được sửa hoàn toàn:
- ✅ **Tạo đơn hàng:** Không trừ tồn kho ngay lập tức
- ✅ **Xác nhận đơn hàng:** Trừ tồn kho khi cập nhật trạng thái
- ✅ **Hủy đơn hàng:** Cộng lại tồn kho khi cập nhật trạng thái
- ✅ **Hoàn hàng:** Cộng lại tồn kho (đã có sẵn)
- ✅ **Transaction safety:** Đảm bảo data consistency
- ✅ **Logging:** Dễ debug và audit

Bây giờ tồn kho sẽ được quản lý chính xác theo workflow đơn hàng! 🎯
