# 🚀 Hướng Dẫn Deploy Frontend lên Vercel

## ❗ Vấn đề 404 với SPA Routing

### Nguyên nh<PERSON>hi deploy Single Page Application (SPA) lên Vercel, việc truy cập trực tiếp vào các route như `/dashboard`, `/products` sẽ gây lỗi 404 vì:
- Server tìm kiếm file tương ứng với path đó
- Nhưng trong SPA, tất cả routing được xử lý bởi JavaScript ở client-side
- Chỉ có file `index.html` tồn tại

### Giải pháp

#### 1. File `vercel.json` (✅ Đã có)
```json
{
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

#### 2. File `public/_redirects` (✅ Đã có - backup)
```
/*    /index.html   200
```

#### 3. Base tag trong `index.html` (✅ Đã có)
```html
<base href="/" />
```

## 📋 Checklist Deploy

### Tr<PERSON>ớ<PERSON> khi deploy:
- [ ] Kiểm tra file `vercel.json` có mặt
- [ ] C<PERSON>u hình `VITE_API_URL` cho production
- [ ] Test build local: `npm run build && npm run preview`
- [ ] Kiểm tra tất cả routes hoạt động trong preview

### Deploy lên Vercel:

#### Cách 1: Vercel CLI
```bash
# Install Vercel CLI
npm i -g vercel

# Login
vercel login

# Deploy
vercel

# Deploy production
vercel --prod
```

#### Cách 2: GitHub Integration
1. Push code lên GitHub
2. Connect repository với Vercel
3. Auto deploy khi push

### Sau khi deploy:
- [ ] Test truy cập trực tiếp vào các route
- [ ] Test refresh trang ở các route khác nhau
- [ ] Kiểm tra API calls hoạt động
- [ ] Test responsive trên mobile

## 🔧 Environment Variables trên Vercel

Trên Vercel Dashboard > Project Settings > Environment Variables:

```
VITE_API_URL = https://your-backend-domain.com/api
VITE_APP_NAME = Phần Mềm Quản Lý Bán Hàng
```

## 🐛 Troubleshooting

### Vẫn bị 404 sau khi deploy
1. Kiểm tra file `vercel.json` có trong root folder
2. Redeploy project
3. Kiểm tra Vercel logs

### API calls không hoạt động
1. Kiểm tra `VITE_API_URL` environment variable
2. Kiểm tra CORS trên backend
3. Kiểm tra network tab trong browser

### Build fails
1. Kiểm tra dependencies trong `package.json`
2. Test build local trước
3. Kiểm tra Node.js version compatibility

## 📱 Test Checklist

Sau khi deploy, test các scenario sau:

### Direct URL Access:
- [ ] `yourapp.vercel.app/` ✅
- [ ] `yourapp.vercel.app/dashboard` ✅
- [ ] `yourapp.vercel.app/products` ✅
- [ ] `yourapp.vercel.app/orders` ✅
- [ ] `yourapp.vercel.app/customers` ✅

### Page Refresh:
- [ ] Refresh tại `/dashboard` ✅
- [ ] Refresh tại `/products` ✅
- [ ] Refresh tại `/orders/123` ✅

### Navigation:
- [ ] Menu navigation hoạt động ✅
- [ ] Browser back/forward buttons ✅
- [ ] Breadcrumb navigation ✅

## 🎯 Kết quả mong đợi

Sau khi áp dụng các fix trên:
- ✅ Truy cập trực tiếp vào bất kỳ route nào đều hoạt động
- ✅ Refresh trang ở bất kỳ đâu đều không bị 404
- ✅ Navigation và routing hoạt động bình thường
- ✅ API calls hoạt động với backend
