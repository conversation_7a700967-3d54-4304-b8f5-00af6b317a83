require('dotenv').config();
const { sequelize } = require('./models');

async function quickCheck() {
  try {
    console.log('🔍 Quick debt check for customer ID 32...');

    // Raw SQL query để kiểm tra trực tiếp
    const [results] = await sequelize.query(`
      SELECT 
        nd.id,
        nd.ho_ten,
        nd.so_dien_thoai,
        nd.email,
        cn.tong_cong_no as debt_from_table,
        (
          SELECT SUM(con_phai_tra) 
          FROM don_hang 
          WHERE khach_hang_id = nd.id
        ) as debt_from_orders
      FROM nguoi_dung nd
      LEFT JOIN cong_no_nguoi_dung cn ON nd.id = cn.nguoi_dung_id
      WHERE nd.id = 32
    `);

    if (results.length === 0) {
      console.log('❌ Customer not found');
      return;
    }

    const customer = results[0];
    console.log('\n📊 Results:');
    console.log(`Customer: ${customer.ho_ten} (${customer.email})`);
    console.log(`Debt from cong_no_nguoi_dung: ${customer.debt_from_table || 0}`);
    console.log(`Debt from orders (con_phai_tra): ${customer.debt_from_orders || 0}`);

    // Kiểm tra đơn hàng chi tiết
    const [orders] = await sequelize.query(`
      SELECT ma_don_hang, tong_phai_tra, tong_da_tra, con_phai_tra, trang_thai
      FROM don_hang 
      WHERE khach_hang_id = 32
      ORDER BY ngay_ban DESC
    `);

    console.log('\n📋 Orders:');
    orders.forEach(order => {
      console.log(`${order.ma_don_hang}: con_phai_tra = ${order.con_phai_tra} (${order.trang_thai})`);
    });

    // Nếu có mismatch, fix nó
    const debtFromTable = customer.debt_from_table || 0;
    const debtFromOrders = customer.debt_from_orders || 0;

    if (Math.abs(debtFromTable - debtFromOrders) > 1) {
      console.log('\n🔧 Fixing mismatch...');
      
      if (customer.debt_from_table === null) {
        // Tạo record mới
        await sequelize.query(`
          INSERT INTO cong_no_nguoi_dung (nguoi_dung_id, tong_cong_no, createdAt, updatedAt)
          VALUES (32, ${debtFromOrders}, NOW(), NOW())
        `);
        console.log(`✅ Created debt record: ${debtFromOrders}`);
      } else {
        // Cập nhật record hiện tại
        await sequelize.query(`
          UPDATE cong_no_nguoi_dung 
          SET tong_cong_no = ${debtFromOrders}, updatedAt = NOW()
          WHERE nguoi_dung_id = 32
        `);
        console.log(`✅ Updated debt record: ${debtFromTable} → ${debtFromOrders}`);
      }
    } else {
      console.log('✅ No fix needed');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await sequelize.close();
  }
}

quickCheck();
