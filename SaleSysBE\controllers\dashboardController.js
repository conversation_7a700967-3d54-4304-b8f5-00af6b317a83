const { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, San<PERSON>, CongNo<PERSON>gu<PERSON>, Payment, sequelize } = require('../models');
const { Op } = require('sequelize');
const dayjs = require('dayjs');

// Lấy thống kê tổng quan dashboard
const getDashboardStats = async (req, res) => {
  try {
    console.log('📊 Getting dashboard statistics...');

    const today = dayjs().startOf('day');
    const yesterday = dayjs().subtract(1, 'day').startOf('day');
    const last7Days = dayjs().subtract(7, 'days').startOf('day');
    const lastWeek = dayjs().subtract(1, 'week').startOf('day');

    // 1. Doanh thu hôm nay
    const todayRevenue = await DonHang.sum('tong_phai_tra', {
      where: {
        ngay_ban: {
          [Op.gte]: today.toDate()
        },
        trang_thai: {
          [Op.in]: ['da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh']
        }
      }
    }) || 0;

    // Doanh thu hôm qua
    const yesterdayRevenue = await DonHang.sum('tong_phai_tra', {
      where: {
        ngay_ban: {
          [Op.between]: [yesterday.toDate(), today.toDate()]
        },
        trang_thai: {
          [Op.in]: ['da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh']
        }
      }
    }) || 0;

    // 2. Đơn hàng mới hôm nay
    const todayOrders = await DonHang.count({
      where: {
        ngay_ban: {
          [Op.gte]: today.toDate()
        }
      }
    });

    // Đơn hàng hôm qua
    const yesterdayOrders = await DonHang.count({
      where: {
        ngay_ban: {
          [Op.between]: [yesterday.toDate(), today.toDate()]
        }
      }
    });

    // 3. Khách hàng mới hôm nay
    const todayCustomers = await NguoiDung.count({
      where: {
        ngay_tao: {
          [Op.gte]: today.toDate()
        },
        loai_nguoi_dung: 'khach_hang'
      }
    });

    // Khách hàng hôm qua
    const yesterdayCustomers = await NguoiDung.count({
      where: {
        ngay_tao: {
          [Op.between]: [yesterday.toDate(), today.toDate()]
        },
        loai_nguoi_dung: 'khach_hang'
      }
    });

    // 4. Sản phẩm bán chạy (tuần này) - sử dụng raw query để tránh lỗi GROUP BY
    const thisWeekProductsResult = await sequelize.query(`
      SELECT COALESCE(SUM(dhsp.so_luong), 0) as total
      FROM don_hang_san_pham dhsp
      INNER JOIN don_hang dh ON dhsp.don_hang_id = dh.id
      WHERE dh.ngay_ban >= :last7Days
      AND dh.trang_thai IN ('da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh')
    `, {
      replacements: { last7Days: last7Days.toDate() },
      type: sequelize.QueryTypes.SELECT
    });
    const thisWeekProducts = thisWeekProductsResult[0]?.total || 0;

    // Sản phẩm tuần trước
    const lastWeekProductsResult = await sequelize.query(`
      SELECT COALESCE(SUM(dhsp.so_luong), 0) as total
      FROM don_hang_san_pham dhsp
      INNER JOIN don_hang dh ON dhsp.don_hang_id = dh.id
      WHERE dh.ngay_ban >= :lastWeek AND dh.ngay_ban < :last7Days
      AND dh.trang_thai IN ('da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh')
    `, {
      replacements: {
        lastWeek: lastWeek.toDate(),
        last7Days: last7Days.toDate()
      },
      type: sequelize.QueryTypes.SELECT
    });
    const lastWeekProducts = lastWeekProductsResult[0]?.total || 0;

    // Tính phần trăm thay đổi
    const revenueChange = yesterdayRevenue > 0 ? 
      ((todayRevenue - yesterdayRevenue) / yesterdayRevenue * 100) : 0;
    
    const ordersChange = yesterdayOrders > 0 ? 
      ((todayOrders - yesterdayOrders) / yesterdayOrders * 100) : 0;
    
    const customersChange = yesterdayCustomers > 0 ? 
      ((todayCustomers - yesterdayCustomers) / yesterdayCustomers * 100) : 0;
    
    const productsChange = lastWeekProducts > 0 ? 
      ((thisWeekProducts - lastWeekProducts) / lastWeekProducts * 100) : 0;

    const stats = {
      today_revenue: todayRevenue,
      revenue_change: revenueChange,
      today_orders: todayOrders,
      orders_change: ordersChange,
      today_customers: todayCustomers,
      customers_change: customersChange,
      week_products_sold: thisWeekProducts,
      products_change: productsChange
    };

    console.log('✅ Dashboard stats retrieved successfully');
    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('❌ Error in getDashboardStats:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy thống kê dashboard',
      error: error.message
    });
  }
};

// Lấy dữ liệu biểu đồ doanh thu 7 ngày
const getSalesChart = async (req, res) => {
  try {
    console.log('📈 Getting sales chart data...');

    const salesData = [];
    
    // Lấy dữ liệu 7 ngày gần nhất
    for (let i = 6; i >= 0; i--) {
      const date = dayjs().subtract(i, 'day');
      const startOfDay = date.startOf('day');
      const endOfDay = date.endOf('day');

      const dailyRevenue = await DonHang.sum('tong_phai_tra', {
        where: {
          ngay_ban: {
            [Op.between]: [startOfDay.toDate(), endOfDay.toDate()]
          },
          trang_thai: {
            [Op.in]: ['da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh']
          }
        }
      }) || 0;

      salesData.push({
        name: date.format('DD/MM'),
        value: dailyRevenue,
        date: date.format('YYYY-MM-DD')
      });
    }

    console.log('✅ Sales chart data retrieved successfully');
    res.json({
      success: true,
      data: salesData
    });

  } catch (error) {
    console.error('❌ Error in getSalesChart:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy dữ liệu biểu đồ doanh thu',
      error: error.message
    });
  }
};

// Lấy đơn hàng gần đây
const getRecentOrders = async (req, res) => {
  try {
    console.log('📋 Getting recent orders...');

    const recentOrders = await DonHang.findAll({
      include: [{
        model: NguoiDung,
        as: 'khachHang',
        attributes: ['ho_ten']
      }],
      order: [['ngay_ban', 'DESC']],
      limit: 10,
      attributes: ['id', 'ma_don_hang', 'tong_phai_tra', 'trang_thai', 'ngay_ban']
    });

    const formattedOrders = recentOrders.map(order => ({
      key: order.id,
      ma_don_hang: order.ma_don_hang,
      khach_hang: order.khachHang?.ho_ten || 'Khách lẻ',
      tong_tien: order.tong_phai_tra,
      trang_thai: order.trang_thai,
      ngay_tao: dayjs(order.ngay_ban).format('YYYY-MM-DD')
    }));

    console.log('✅ Recent orders retrieved successfully');
    res.json({
      success: true,
      data: formattedOrders
    });

  } catch (error) {
    console.error('❌ Error in getRecentOrders:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy đơn hàng gần đây',
      error: error.message
    });
  }
};

// Lấy sản phẩm bán chạy
const getTopProducts = async (req, res) => {
  try {
    console.log('🏆 Getting top products...');

    const topProducts = await DonHangSanPham.findAll({
      attributes: [
        'phien_ban_san_pham_id',
        'ten_san_pham',
        [sequelize.fn('SUM', sequelize.col('DonHangSanPham.so_luong')), 'total_sold']
      ],
      include: [{
        model: DonHang,
        as: 'donHang',
        where: {
          ngay_ban: {
            [Op.gte]: dayjs().subtract(30, 'days').toDate()
          },
          trang_thai: {
            [Op.in]: ['da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh']
          }
        },
        attributes: []
      }],
      group: ['DonHangSanPham.phien_ban_san_pham_id', 'DonHangSanPham.ten_san_pham'],
      order: [[sequelize.fn('SUM', sequelize.col('DonHangSanPham.so_luong')), 'DESC']],
      limit: 5,
      raw: true
    });

    const formattedProducts = topProducts.map((product, index) => ({
      title: product.ten_san_pham || `Sản phẩm ${product.phien_ban_san_pham_id}`,
      description: 'Sản phẩm bán chạy',
      avatar: ['📱', '💻', '🎧', '⌚', '📷'][index] || '📦',
      sales: parseInt(product.total_sold) || 0
    }));

    console.log('✅ Top products retrieved successfully');
    res.json({
      success: true,
      data: formattedProducts
    });

  } catch (error) {
    console.error('❌ Error in getTopProducts:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy sản phẩm bán chạy',
      error: error.message
    });
  }
};

module.exports = {
  getDashboardStats,
  getSalesChart,
  getRecentOrders,
  getTopProducts
};
