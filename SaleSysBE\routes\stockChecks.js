const express = require('express');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { requirePermission } = require('../middleware/auth');
const {
  getStockChecks,
  createStockCheck,
  getStockCheckDetails,
  addProductToStockCheck,
  getAvailableProducts
} = require('../controllers/stockCheckController');

const router = express.Router();

/**
 * @route GET /api/stock-checks
 * @desc Lấy danh sách phiếu kiểm kê
 * @access Private (Cần quyền XEM_KIEM_KE)
 */
router.get('/', requirePermission('XEM_KIEM_KE'), asyncHandler(getStockChecks));

/**
 * @route POST /api/stock-checks
 * @desc Tạo phiếu kiểm kê mới
 * @access Private (Cần quyền THEM_KIEM_KE)
 */
router.post('/', requirePermission('THEM_KIEM_KE'), asyncHandler(createStockCheck));

/**
 * @route GET /api/stock-checks/:id/details
 * @desc Lấy chi tiết phiếu kiểm kê
 * @access Private (Cần quyền XEM_KIEM_KE)
 */
router.get('/:id/details', requirePermission('XEM_KIEM_KE'), asyncHandler(getStockCheckDetails));

/**
 * @route POST /api/stock-checks/:id/products
 * @desc Thêm sản phẩm vào phiếu kiểm kê
 * @access Private (Cần quyền SUA_KIEM_KE)
 */
router.post('/:id/products', requirePermission('SUA_KIEM_KE'), asyncHandler(addProductToStockCheck));

/**
 * @route GET /api/stock-checks/:id/available-products
 * @desc Lấy danh sách sản phẩm có thể thêm vào phiếu kiểm kê
 * @access Private (Cần quyền XEM_KIEM_KE)
 */
router.get('/:id/available-products', requirePermission('XEM_KIEM_KE'), asyncHandler(getAvailableProducts));

module.exports = router;
