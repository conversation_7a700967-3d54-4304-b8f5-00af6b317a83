const { Sequelize } = require('sequelize');
const config = require('./config/database');

// Tạo kết nối database
const sequelize = new Sequelize(config.development);

async function fixRolePermissions() {
  try {
    console.log('🔧 Fixing role permissions...');
    console.log('');

    // L<PERSON>y danh sách roles và permissions
    const roles = await sequelize.query(
      'SELECT id, ma_vai_tro FROM vai_tro',
      { type: Sequelize.QueryTypes.SELECT }
    );

    const permissions = await sequelize.query(
      'SELECT id, ma_quyen FROM quyen',
      { type: Sequelize.QueryTypes.SELECT }
    );

    // Helper function để thêm quyền cho role
    async function addPermissionToRole(roleName, permissionCode) {
      const role = roles.find(r => r.ma_vai_tro === roleName);
      const permission = permissions.find(p => p.ma_quyen === permissionCode);

      if (!role || !permission) {
        console.log(`⚠️ Role ${roleName} or permission ${permissionCode} not found`);
        return;
      }

      // Kiểm tra xem đã có quyền chưa
      const existing = await sequelize.query(
        `SELECT * FROM vai_tro_quyen WHERE vai_tro_id = ${role.id} AND quyen_id = ${permission.id}`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (existing.length === 0) {
        await sequelize.query(
          `INSERT INTO vai_tro_quyen (vai_tro_id, quyen_id) VALUES (${role.id}, ${permission.id})`
        );
        console.log(`✅ Added ${permissionCode} to ${roleName}`);
      } else {
        console.log(`⚠️ ${roleName} already has ${permissionCode}`);
      }
    }

    // Thêm quyền XEM_TAG cho ADMIN và QUAN_LY (nếu chưa có)
    console.log('🏷️ Adding XEM_TAG permission...');
    await addPermissionToRole('ADMIN', 'XEM_TAG');
    await addPermissionToRole('QUAN_LY', 'XEM_TAG');

    // Thêm quyền cho KE_TOAN (cần để xem filter data trong báo cáo)
    console.log('');
    console.log('👨‍💼 Adding permissions for KE_TOAN role...');
    await addPermissionToRole('KE_TOAN', 'XEM_LOAI_SAN_PHAM');
    await addPermissionToRole('KE_TOAN', 'XEM_NHAN_HIEU');
    await addPermissionToRole('KE_TOAN', 'XEM_NHOM_KHACH_HANG');
    await addPermissionToRole('KE_TOAN', 'XEM_TAG');

    // Thêm quyền cho NHAN_VIEN_BAN_HANG (cần để xem filter data)
    console.log('');
    console.log('👨‍💻 Adding permissions for NHAN_VIEN_BAN_HANG role...');
    await addPermissionToRole('NHAN_VIEN_BAN_HANG', 'XEM_LOAI_SAN_PHAM');
    await addPermissionToRole('NHAN_VIEN_BAN_HANG', 'XEM_NHAN_HIEU');
    await addPermissionToRole('NHAN_VIEN_BAN_HANG', 'XEM_NHOM_KHACH_HANG');
    await addPermissionToRole('NHAN_VIEN_BAN_HANG', 'XEM_TAG');
    // Không thêm XUAT_BAO_CAO cho nhân viên bán hàng

    console.log('');
    console.log('✅ Role permissions fixed successfully!');
    console.log('');
    console.log('📋 Summary of changes:');
    console.log('  🔐 ADMIN: Added XEM_TAG (if missing)');
    console.log('  🔐 QUAN_LY: Added XEM_TAG (if missing)');
    console.log('  🔐 KE_TOAN: Added XEM_LOAI_SAN_PHAM, XEM_NHAN_HIEU, XEM_NHOM_KHACH_HANG, XEM_TAG');
    console.log('  🔐 NHAN_VIEN_BAN_HANG: Added XEM_LOAI_SAN_PHAM, XEM_NHAN_HIEU, XEM_NHOM_KHACH_HANG, XEM_TAG');
    console.log('');
    console.log('🎯 Now all roles have appropriate permissions for Reports functionality!');

  } catch (error) {
    console.error('❌ Error fixing role permissions:', error);
  } finally {
    await sequelize.close();
  }
}

fixRolePermissions();
