const { Sequelize } = require('sequelize');
const config = require('./config/database');

// Tạo kết nối database
const sequelize = new Sequelize(config.development);

async function checkPermissions() {
  try {
    console.log('🔍 Checking permissions in database...');
    console.log('');

    // Kiểm tra các quyền cần thiết cho Reports
    const requiredPermissions = [
      'XEM_BAO_CAO',
      'XUAT_BAO_CAO',
      'XEM_KHACH_HANG',
      'XEM_LOAI_SAN_PHAM',
      'XEM_NHAN_HIEU',
      'XEM_NHOM_KHACH_HANG',
      'XEM_TAG'
    ];

    console.log('📋 Required permissions for Reports:');
    for (const permission of requiredPermissions) {
      const result = await sequelize.query(
        `SELECT id, ma_quyen, ten_quyen FROM quyen WHERE ma_quyen = '${permission}'`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (result.length > 0) {
        console.log(`✅ ${permission}: ${result[0].ten_quyen}`);
      } else {
        console.log(`❌ ${permission}: MISSING`);
      }
    }

    console.log('');
    console.log('👤 Checking role permissions...');
    
    // Kiểm tra quyền của từng role
    const roles = ['ADMIN', 'QUAN_LY', 'KE_TOAN', 'NHAN_VIEN_BAN_HANG'];
    
    for (const roleName of roles) {
      console.log(`\n🔐 Role: ${roleName}`);
      
      const rolePermissions = await sequelize.query(`
        SELECT q.ma_quyen, q.ten_quyen 
        FROM quyen q
        JOIN vai_tro_quyen vtq ON q.id = vtq.quyen_id
        JOIN vai_tro vt ON vtq.vai_tro_id = vt.id
        WHERE vt.ma_vai_tro = '${roleName}'
        AND q.ma_quyen IN ('XEM_BAO_CAO', 'XUAT_BAO_CAO', 'XEM_KHACH_HANG', 'XEM_LOAI_SAN_PHAM', 'XEM_NHAN_HIEU', 'XEM_NHOM_KHACH_HANG')
        ORDER BY q.ma_quyen
      `, { type: Sequelize.QueryTypes.SELECT });

      if (rolePermissions.length > 0) {
        rolePermissions.forEach(perm => {
          console.log(`  ✅ ${perm.ma_quyen}`);
        });
      } else {
        console.log(`  ❌ No relevant permissions found`);
      }

      // Kiểm tra các quyền còn thiếu
      const missingPermissions = requiredPermissions.filter(reqPerm => 
        !rolePermissions.some(rolePerm => rolePerm.ma_quyen === reqPerm)
      );

      if (missingPermissions.length > 0) {
        console.log(`  ⚠️ Missing permissions:`);
        missingPermissions.forEach(perm => {
          console.log(`    - ${perm}`);
        });
      }
    }

    console.log('');
    console.log('📊 Summary:');
    
    // Tổng số quyền trong hệ thống
    const totalPermissions = await sequelize.query(
      'SELECT COUNT(*) as count FROM quyen',
      { type: Sequelize.QueryTypes.SELECT }
    );
    console.log(`Total permissions in system: ${totalPermissions[0].count}`);

    // Tổng số role
    const totalRoles = await sequelize.query(
      'SELECT COUNT(*) as count FROM vai_tro',
      { type: Sequelize.QueryTypes.SELECT }
    );
    console.log(`Total roles in system: ${totalRoles[0].count}`);

    // Kiểm tra user admin có đủ quyền không
    const adminPermissions = await sequelize.query(`
      SELECT COUNT(*) as count
      FROM quyen q
      JOIN vai_tro_quyen vtq ON q.id = vtq.quyen_id
      JOIN vai_tro vt ON vtq.vai_tro_id = vt.id
      WHERE vt.ma_vai_tro = 'ADMIN'
    `, { type: Sequelize.QueryTypes.SELECT });

    console.log(`ADMIN role has ${adminPermissions[0].count} permissions`);

    if (adminPermissions[0].count === totalPermissions[0].count) {
      console.log('✅ ADMIN has all permissions');
    } else {
      console.log('⚠️ ADMIN is missing some permissions');
    }

  } catch (error) {
    console.error('❌ Error checking permissions:', error);
  } finally {
    await sequelize.close();
  }
}

checkPermissions();
