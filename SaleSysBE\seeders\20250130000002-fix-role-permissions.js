'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    console.log('🔧 Fixing role permissions for Reports...');

    // Helper function để thêm quyền cho role
    async function addPermissionToRole(roleName, permissionCode) {
      const role = await queryInterface.sequelize.query(
        `SELECT id FROM vai_tro WHERE ma_vai_tro = '${roleName}'`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      const permission = await queryInterface.sequelize.query(
        `SELECT id FROM quyen WHERE ma_quyen = '${permissionCode}'`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (role.length === 0 || permission.length === 0) {
        console.log(`⚠️ Role ${roleName} or permission ${permissionCode} not found`);
        return;
      }

      const roleId = role[0].id;
      const permissionId = permission[0].id;

      // <PERSON><PERSON><PERSON> tra xem đã có quyền chưa
      const existing = await queryInterface.sequelize.query(
        `SELECT * FROM vai_tro_quyen WHERE vai_tro_id = ${roleId} AND quyen_id = ${permissionId}`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (existing.length === 0) {
        await queryInterface.bulkInsert('vai_tro_quyen', [{
          vai_tro_id: roleId,
          quyen_id: permissionId
        }], {});
        console.log(`✅ Added ${permissionCode} to ${roleName}`);
      } else {
        console.log(`⚠️ ${roleName} already has ${permissionCode}`);
      }
    }

    // Thêm quyền XEM_TAG cho ADMIN và QUAN_LY
    console.log('🏷️ Adding XEM_TAG permission...');
    await addPermissionToRole('ADMIN', 'XEM_TAG');
    await addPermissionToRole('QUAN_LY', 'XEM_TAG');

    // Thêm quyền cho KE_TOAN
    console.log('👨‍💼 Adding permissions for KE_TOAN role...');
    await addPermissionToRole('KE_TOAN', 'XEM_LOAI_SAN_PHAM');
    await addPermissionToRole('KE_TOAN', 'XEM_NHAN_HIEU');
    await addPermissionToRole('KE_TOAN', 'XEM_NHOM_KHACH_HANG');
    await addPermissionToRole('KE_TOAN', 'XEM_TAG');

    // Thêm quyền cho NHAN_VIEN_BAN_HANG
    console.log('👨‍💻 Adding permissions for NHAN_VIEN_BAN_HANG role...');
    await addPermissionToRole('NHAN_VIEN_BAN_HANG', 'XEM_LOAI_SAN_PHAM');
    await addPermissionToRole('NHAN_VIEN_BAN_HANG', 'XEM_NHAN_HIEU');
    await addPermissionToRole('NHAN_VIEN_BAN_HANG', 'XEM_NHOM_KHACH_HANG');
    await addPermissionToRole('NHAN_VIEN_BAN_HANG', 'XEM_TAG');

    console.log('✅ Role permissions fixed successfully!');
  },

  async down(queryInterface, Sequelize) {
    // Xóa các quyền đã thêm (nếu cần rollback)
    console.log('🔄 Rolling back role permission changes...');
    
    const rolesToRevert = [
      { role: 'KE_TOAN', permissions: ['XEM_LOAI_SAN_PHAM', 'XEM_NHAN_HIEU', 'XEM_NHOM_KHACH_HANG', 'XEM_TAG'] },
      { role: 'NHAN_VIEN_BAN_HANG', permissions: ['XEM_LOAI_SAN_PHAM', 'XEM_NHAN_HIEU', 'XEM_NHOM_KHACH_HANG', 'XEM_TAG'] }
    ];

    for (const roleData of rolesToRevert) {
      const role = await queryInterface.sequelize.query(
        `SELECT id FROM vai_tro WHERE ma_vai_tro = '${roleData.role}'`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (role.length > 0) {
        const roleId = role[0].id;

        for (const permissionCode of roleData.permissions) {
          const permission = await queryInterface.sequelize.query(
            `SELECT id FROM quyen WHERE ma_quyen = '${permissionCode}'`,
            { type: Sequelize.QueryTypes.SELECT }
          );

          if (permission.length > 0) {
            const permissionId = permission[0].id;
            
            await queryInterface.bulkDelete('vai_tro_quyen', {
              vai_tro_id: roleId,
              quyen_id: permissionId
            }, {});
            
            console.log(`🗑️ Removed ${permissionCode} from ${roleData.role}`);
          }
        }
      }
    }
  }
};
