# 🎯 Thêm Tính Năng Cập Nhật Trạng Thái Hàng Loạt

## ✅ **Tính năng đã thêm:**

### **🎯 Yêu cầu:**
1. **Cột checkbox** để chọn nhiều đơn hàng
2. **Button cập nhật trạng thái** cạnh button "Xóa bộ lọc"
3. **Modal cập nhật hàng loạt** với dropdown chọn trạng thái

### **✅ Đã hoàn thành:**
- ✅ Thêm cột checkbox với rowSelection
- ✅ <PERSON><PERSON> "Cập nhật trạng thái" hiển thị số lượng đã chọn
- ✅ Modal cập nhật trạng thái hàng loạt
- ✅ Logic xử lý cập nhật nhiều đơn hàng cùng lúc

## 🔧 **Chi tiết thay đổi:**

### **File: SaleSysFE/src/pages/Orders/OrdersList.jsx**

#### **1. Thêm state quản lý:**

```javascript
// Thêm state cho checkbox selection và modal
const [selectedRowKeys, setSelectedRowKeys] = useState([]);
const [bulkUpdateModal, setBulkUpdateModal] = useState({
  visible: false,
  newStatus: "",
});
```

#### **2. Thêm rowSelection cho Table:**

```javascript
<Table
  columns={columns}
  dataSource={orders}
  rowKey="id"
  loading={isLoading}
  rowSelection={{
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record) => ({
      disabled: false,
      name: record.ma_don_hang,
    }),
  }}
  // ... other props
/>
```

#### **3. Thêm button cập nhật trạng thái:**

```javascript
<Space>
  <Button
    icon={<FilterOutlined />}
    onClick={() => {
      setSearchText("");
      setStatusFilter("");
      setDateRange([]);
    }}
  >
    Xóa bộ lọc
  </Button>
  <Button
    type="primary"
    disabled={selectedRowKeys.length === 0}
    onClick={() => setBulkUpdateModal({ visible: true, newStatus: "" })}
  >
    Cập nhật trạng thái ({selectedRowKeys.length})
  </Button>
</Space>
```

#### **4. Thêm Modal cập nhật hàng loạt:**

```javascript
<Modal
  title="Cập nhật trạng thái hàng loạt"
  open={bulkUpdateModal.visible}
  onOk={handleBulkUpdateStatus}
  onCancel={() => setBulkUpdateModal({ visible: false, newStatus: "" })}
  confirmLoading={updateStatusMutation.isLoading}
>
  <div style={{ marginBottom: "16px" }}>
    <Text>
      Bạn đang cập nhật trạng thái cho <strong>{selectedRowKeys.length}</strong> đơn hàng
    </Text>
  </div>
  
  <div>
    <Text strong>Chọn trạng thái mới:</Text>
    <Select
      placeholder="Chọn trạng thái"
      value={bulkUpdateModal.newStatus}
      onChange={(value) => setBulkUpdateModal(prev => ({ ...prev, newStatus: value }))}
      style={{ width: "100%", marginTop: "8px" }}
    >
      {ORDER_STATUS.map((status) => (
        <Option key={status.value} value={status.value}>
          <Tag color={getOrderStatusInfo(status.value).color}>
            {status.label}
          </Tag>
        </Option>
      ))}
    </Select>
  </div>

  <Alert
    message="Lưu ý"
    description="Thao tác này sẽ cập nhật trạng thái cho tất cả đơn hàng đã chọn. Việc thay đổi trạng thái có thể ảnh hưởng đến tồn kho và công nợ."
    type="warning"
    showIcon
    style={{ marginTop: "16px" }}
  />
</Modal>
```

#### **5. Thêm function xử lý cập nhật hàng loạt:**

```javascript
const handleBulkUpdateStatus = async () => {
  if (!bulkUpdateModal.newStatus) {
    message.warning("Vui lòng chọn trạng thái mới");
    return;
  }

  if (selectedRowKeys.length === 0) {
    message.warning("Vui lòng chọn ít nhất một đơn hàng");
    return;
  }

  try {
    // Cập nhật từng đơn hàng
    const updatePromises = selectedRowKeys.map(orderId => 
      updateStatusMutation.mutateAsync({ 
        id: orderId, 
        status: bulkUpdateModal.newStatus 
      })
    );

    await Promise.all(updatePromises);
    
    message.success(`Đã cập nhật trạng thái cho ${selectedRowKeys.length} đơn hàng`);
    setBulkUpdateModal({ visible: false, newStatus: "" });
    setSelectedRowKeys([]);
  } catch (error) {
    console.error("Bulk update error:", error);
    message.error("Có lỗi xảy ra khi cập nhật trạng thái");
  }
};
```

## 🎨 **UI/UX Features:**

### **1. Checkbox Selection:**
```
☐ Checkbox header → Select/Deselect all
☐ Checkbox mỗi row → Select individual orders
✅ Visual feedback khi chọn
```

### **2. Button State:**
```
Disabled: "Cập nhật trạng thái (0)" → Khi chưa chọn đơn hàng nào
Enabled: "Cập nhật trạng thái (5)" → Hiển thị số lượng đã chọn
```

### **3. Modal Features:**
```
📋 Hiển thị số lượng đơn hàng được chọn
🎯 Dropdown chọn trạng thái với màu sắc
⚠️ Warning alert về tác động của việc thay đổi
🔄 Loading state khi đang xử lý
```

### **4. Status Dropdown:**
```
🏷️ Mỗi option hiển thị Tag với màu tương ứng:
- Chờ xử lý (Orange)
- Đã xác nhận (Blue) 
- Đã đóng gói (Cyan)
- Đã giao (Green)
- Hoàn thành (Success)
- Hủy (Red)
- Hoàn hàng (Purple)
```

## 🔄 **Workflow:**

### **1. Chọn đơn hàng:**
```
User tick checkbox → selectedRowKeys updated → Button enabled
```

### **2. Mở modal:**
```
Click "Cập nhật trạng thái" → Modal mở → Hiển thị số lượng đã chọn
```

### **3. Chọn trạng thái:**
```
Select dropdown → Chọn trạng thái mới → Preview với Tag màu
```

### **4. Xác nhận cập nhật:**
```
Click "OK" → Promise.all() → Cập nhật từng đơn hàng → Success message
```

### **5. Reset state:**
```
Success → Clear selectedRowKeys → Close modal → Refresh table
```

## 🚀 **Lợi ích:**

### **1. Hiệu quả:**
- ✅ **Cập nhật hàng loạt** thay vì từng đơn một
- ✅ **Tiết kiệm thời gian** cho admin
- ✅ **Batch processing** nhiều đơn hàng cùng lúc

### **2. UX tốt:**
- ✅ **Visual feedback** rõ ràng
- ✅ **Validation** trước khi thực hiện
- ✅ **Loading states** trong quá trình xử lý
- ✅ **Success/Error messages** phù hợp

### **3. Tính năng:**
- ✅ **Select all/none** với header checkbox
- ✅ **Individual selection** cho từng đơn hàng
- ✅ **Status preview** với màu sắc
- ✅ **Warning alerts** về tác động

### **4. Consistency:**
- ✅ **Sử dụng existing hooks** (useUpdateOrderStatus)
- ✅ **Consistent styling** với UI hiện tại
- ✅ **Same validation** như cập nhật đơn lẻ

## 🎯 **Use Cases:**

### **1. Xác nhận hàng loạt:**
```
Chọn nhiều đơn "Chờ xử lý" → Cập nhật thành "Đã xác nhận"
→ Tự động trừ tồn kho cho tất cả đơn hàng
```

### **2. Đóng gói hàng loạt:**
```
Chọn nhiều đơn "Đã xác nhận" → Cập nhật thành "Đã đóng gói"
→ Chuẩn bị giao hàng
```

### **3. Hủy hàng loạt:**
```
Chọn nhiều đơn lỗi → Cập nhật thành "Hủy"
→ Tự động hoàn tồn kho
```

### **4. Hoàn thành hàng loạt:**
```
Chọn nhiều đơn "Đã giao" → Cập nhật thành "Hoàn thành"
→ Finalize orders
```

## 🎉 **Hoàn thành:**

Tính năng cập nhật trạng thái hàng loạt đã được thêm hoàn toàn:
- ✅ **Cột checkbox** cho multi-selection
- ✅ **Button cập nhật** với counter
- ✅ **Modal hàng loạt** với dropdown trạng thái
- ✅ **Batch processing** với Promise.all()
- ✅ **Error handling** và validation
- ✅ **UX tốt** với loading states và messages

Bây giờ admin có thể cập nhật trạng thái cho nhiều đơn hàng cùng lúc một cách hiệu quả! 🎯
