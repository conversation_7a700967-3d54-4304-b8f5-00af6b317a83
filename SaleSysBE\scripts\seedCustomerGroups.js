const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ang<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, sequelize } = require('../models');

async function seedCustomerGroups() {
  try {
    console.log('🌱 Seeding customer groups...');

    // 1. Create customer groups
    const groups = await NhomKhachHang.bulkCreate([
      {
        ten_nhom: 'Kh<PERSON>ch hàng VIP',
        ma_nhom: 'VIP',
        mo_ta: '<PERSON>h<PERSON>ch hàng có tổng chi tiêu từ 10,000,000 VND trở lên',
        loai_nhom: 1,
        ngay_tao: new Date()
      },
      {
        ten_nhom: 'Kh<PERSON>ch hàng Thân thiết',
        ma_nhom: 'LOYAL',
        mo_ta: '<PERSON>h<PERSON>ch hàng có tổng chi tiêu từ 5,000,000 - 9,999,999 VND',
        loai_nhom: 1,
        ngay_tao: new Date()
      },
      {
        ten_nhom: '<PERSON>h<PERSON>ch hàng Thường xuyên',
        ma_nhom: 'REGULAR',
        mo_ta: '<PERSON><PERSON><PERSON><PERSON> hàng có tổng chi tiêu từ 1,000,000 - 4,999,999 VND',
        loai_nhom: 1,
        ngay_tao: new Date()
      },
      {
        ten_nhom: 'Khách hàng Mới',
        ma_nhom: 'NEW',
        mo_ta: 'Khách hàng có tổng chi tiêu dưới 1,000,000 VND',
        loai_nhom: 1,
        ngay_tao: new Date()
      },
      {
        ten_nhom: 'Khách hàng Doanh nghiệp',
        ma_nhom: 'BUSINESS',
        mo_ta: 'Khách hàng là doanh nghiệp, công ty',
        loai_nhom: 1,
        ngay_tao: new Date()
      },
      {
        ten_nhom: 'Khách hàng Bán lẻ',
        ma_nhom: 'RETAIL',
        mo_ta: 'Khách hàng mua lẻ, cá nhân',
        loai_nhom: 1,
        ngay_tao: new Date()
      }
    ], { ignoreDuplicates: true });

    console.log(`✅ Created ${groups.length} customer groups`);

    // 2. Get existing customers
    const customers = await NguoiDung.findAll({
      where: { loai_nguoi_dung: 'khach_hang' },
      attributes: ['id', 'ho_ten'],
      limit: 10
    });

    console.log(`📋 Found ${customers.length} customers`);

    // 3. Assign customers to groups
    if (customers.length > 0) {
      const assignments = [];
      
      customers.forEach((customer, index) => {
        // Assign to spending-based groups (1-4) in rotation
        const spendingGroupId = (index % 4) + 1;
        assignments.push({
          nguoi_dung_id: customer.id,
          nhom_khach_hang_id: spendingGroupId
        });
        
        // Also assign to retail group (group 6)
        assignments.push({
          nguoi_dung_id: customer.id,
          nhom_khach_hang_id: 6
        });
        
        // Assign first customer to business group as well
        if (index === 0) {
          assignments.push({
            nguoi_dung_id: customer.id,
            nhom_khach_hang_id: 5
          });
        }
      });

      await NhomKhachHangNguoiDung.bulkCreate(assignments, { ignoreDuplicates: true });
      console.log(`✅ Created ${assignments.length} group assignments`);
    }

    // 4. Display results
    const groupsWithCounts = await NhomKhachHang.findAll({
      attributes: [
        'id',
        'ten_nhom',
        'ma_nhom',
        [sequelize.fn('COUNT', sequelize.col('nguoiDungList.id')), 'customer_count']
      ],
      include: [{
        model: NguoiDung,
        as: 'nguoiDungList',
        attributes: [],
        through: { attributes: [] }
      }],
      group: ['NhomKhachHang.id'],
      order: [['id', 'ASC']]
    });

    console.log('\n📊 Customer Groups Summary:');
    groupsWithCounts.forEach(group => {
      console.log(`- ${group.ten_nhom} (${group.ma_nhom}): ${group.dataValues.customer_count} customers`);
    });

    console.log('\n🎉 Customer groups seeded successfully!');
    process.exit(0);

  } catch (error) {
    console.error('❌ Error seeding customer groups:', error);
    process.exit(1);
  }
}

seedCustomerGroups();
