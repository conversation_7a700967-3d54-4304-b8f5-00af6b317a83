const express = require("express");
const { asyncHandler, AppError } = require("../middleware/errorHandler");
const { requirePermission } = require("../middleware/auth");
const {
  getDebtList,
  getCustomerDebtDetail,
  createPayment,
  getDebtReport,
  createInternalNote,
  getInternalNotes,
  syncDebtFromOrders,
  testSimplePayment,
} = require("../controllers/debtController");

const router = express.Router();

/**
 * @route GET /api/debt
 * @desc Lấy danh sách công nợ khách hàng
 * @access Private (Cần quyền XEM_CONG_NO)
 */
router.get("/", requirePermission("XEM_CONG_NO"), asyncHandler(getDebtList));

/**
 * @route GET /api/debt/customer/:customerId
 * @desc Lấy chi tiết công nợ của khách hàng
 * @access Private (Cần quyền XEM_CONG_NO)
 */
router.get(
  "/customer/:customerId",
  requirePermission("XEM_CONG_NO"),
  asyncHandler(getCustomerDebtDetail)
);

/**
 * @route POST /api/debt/payment
 * @desc Tạo thanh toán công nợ
 * @access Private (Cần quyền THEM_THANH_TOAN)
 */
router.post(
  "/payment",
  requirePermission("THEM_THANH_TOAN"),
  asyncHandler(createPayment)
);

/**
 * @route GET /api/debt/report
 * @desc Lấy báo cáo công nợ tổng hợp
 * @access Private (Cần quyền XEM_BAO_CAO)
 */
router.get(
  "/report",
  requirePermission("XEM_BAO_CAO"),
  asyncHandler(getDebtReport)
);

/**
 * @route POST /api/debt/notes
 * @desc Tạo ghi chú nội bộ về công nợ
 * @access Private (Cần quyền THEM_GHI_CHU)
 */
router.post(
  "/notes",
  requirePermission("THEM_GHI_CHU"),
  asyncHandler(createInternalNote)
);

/**
 * @route GET /api/debt/notes/:customerId
 * @desc Lấy danh sách ghi chú nội bộ của khách hàng
 * @access Private (Cần quyền XEM_GHI_CHU)
 */
router.get(
  "/notes/:customerId",
  requirePermission("XEM_GHI_CHU"),
  asyncHandler(getInternalNotes)
);

/**
 * @route POST /api/debt/sync
 * @desc Đồng bộ hóa công nợ từ đơn hàng
 * @access Private (Cần quyền QUAN_LY_CONG_NO)
 */
router.post("/sync", asyncHandler(syncDebtFromOrders)); // Tạm bỏ permission để test

/**
 * @route POST /api/debt/test-payment
 * @desc Test endpoint cho thanh toán (development only)
 * @access Private
 */
router.post("/test-payment", asyncHandler(testSimplePayment));

/**
 * @route POST /api/debt/seed-test-data
 * @desc Tạo dữ liệu test cho báo cáo công nợ (development only)
 * @access Private
 */
router.post(
  "/seed-test-data",
  asyncHandler(async (req, res) => {
    try {
      const { seedDebtReportData } = require("../scripts/seedDebtReportData");
      await seedDebtReportData();

      res.json({
        success: true,
        message: "Tạo dữ liệu test thành công",
      });
    } catch (error) {
      console.error("❌ Error seeding test data:", error);
      res.status(500).json({
        success: false,
        message: "Lỗi khi tạo dữ liệu test",
        error: error.message,
      });
    }
  })
);

module.exports = router;
