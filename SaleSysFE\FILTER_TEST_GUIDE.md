# 🧪 Hướng Dẫn Test Bộ Lọc Reports

## ❗ Vấn đề đã được sửa

Đã cập nhật backend để hỗ trợ các bộ lọc nâng cao trong tất cả các API báo cáo:

### ✅ Các bộ lọc được hỗ trợ:

#### 1. **<PERSON><PERSON> lọc chung:**

- `customerGroups` - <PERSON><PERSON><PERSON><PERSON> khách hàng
- `customers` - Kh<PERSON>ch hàng cụ thể
- `orderStatus` - Trạng thái đơn hàng
- `minOrderValue` - Giá trị đơn hàng tối thiểu
- `maxOrderValue` - Giá trị đơn hàng tối đa

#### 2. **Bộ lọc cho sản phẩm:**

- `productCategories` - <PERSON><PERSON> mục sản phẩm

#### 3. **Bộ lọc cho hoạt động kinh doanh:**

- `customerTypes` - <PERSON><PERSON><PERSON> khách hàng
- `paymentMethods` - <PERSON><PERSON><PERSON>ng thức thanh toán
- `salesChannels` - <PERSON><PERSON><PERSON> b<PERSON> hàng
- `minQuantity` - <PERSON><PERSON> lượng tối thiểu
- `maxQuantity` - Số lượng tối đa

## 🔧 Cách test:

### 1. **Khởi động backend và frontend:**

```bash
# Backend
cd SaleSysBE
npm start

# Frontend
cd SaleSysFE
npm run dev
```

### 2. **Kiểm tra console logs backend:**

Mở terminal backend và quan sát các log sau khi áp dụng bộ lọc:

- `📈 Getting business activity report...` - Hiển thị tham số nhận được
- `🔍 Raw query params:` - Query parameters thô từ frontend
- `🔍 Parsed filter params:` - Tham số đã được parse thành array

### 3. **Kiểm tra Network tab:**

Mở DevTools > Network > XHR để xem:

- Request URL có chứa query parameters
- Response data thay đổi theo bộ lọc

### 2. **Test các scenario:**

#### **Scenario 1: Lọc theo nhóm khách hàng**

1. Vào trang Reports
2. Click "Bộ lọc nâng cao"
3. Chọn 1-2 nhóm khách hàng
4. Click "Áp dụng"
5. ✅ Kiểm tra: Chỉ hiển thị dữ liệu của nhóm đã chọn

#### **Scenario 2: Lọc theo giá trị đơn hàng**

1. Nhập "Giá trị đơn hàng từ": 100000
2. Nhập "Giá trị đơn hàng đến": 1000000
3. Click "Áp dụng"
4. ✅ Kiểm tra: Chỉ hiển thị đơn hàng trong khoảng giá trị

#### **Scenario 3: Lọc theo trạng thái đơn hàng**

1. Chọn trạng thái: "Đã hoàn thành"
2. Click "Áp dụng"
3. ✅ Kiểm tra: Chỉ hiển thị đơn hàng đã hoàn thành

#### **Scenario 4: Lọc kết hợp**

1. Chọn nhóm khách hàng + trạng thái + giá trị
2. Click "Áp dụng"
3. ✅ Kiểm tra: Dữ liệu phù hợp với tất cả điều kiện

### 3. **Kiểm tra console logs:**

Mở Developer Tools > Console để xem:

- Frontend: `Report params:` - Tham số được gửi
- Backend: `Getting business activity report...` - Tham số nhận được
- Backend: `Raw query params:` - Query parameters thô

### 4. **Test từng tab báo cáo:**

#### **Tab Tổng quan:**

- API: `/reports/overview`
- Bộ lọc: customerGroups, customers, orderStatus, minOrderValue, maxOrderValue

#### **Tab Sản phẩm:**

- API: `/reports/products`
- Bộ lọc: customerGroups, customers, productCategories, orderStatus, minOrderValue, maxOrderValue

#### **Tab Hoạt động kinh doanh:**

- API: `/reports/business-activity`
- Bộ lọc: Tất cả các bộ lọc

#### **Tab Nhóm khách hàng:**

- API: `/reports/customer-groups`
- Bộ lọc: customerGroups, orderStatus, minOrderValue, maxOrderValue

## 🐛 Troubleshooting:

### **Vấn đề: Bộ lọc không hoạt động**

1. Kiểm tra console logs
2. Kiểm tra Network tab trong DevTools
3. Đảm bảo backend đang chạy
4. Kiểm tra dữ liệu test có phù hợp với bộ lọc không

### **Vấn đề: Lỗi SQL**

1. Kiểm tra backend console
2. Đảm bảo database có dữ liệu
3. Kiểm tra foreign key relationships

### **Vấn đề: Frontend không gửi tham số**

1. Kiểm tra `reportParams` trong console
2. Kiểm tra `advancedFilters` state
3. Đảm bảo form được submit đúng cách

## 📊 Kết quả mong đợi:

Sau khi áp dụng bộ lọc:

- ✅ Bảng dữ liệu cập nhật theo bộ lọc
- ✅ Charts/graphs cập nhật tương ứng
- ✅ Số liệu thống kê thay đổi
- ✅ Badge hiển thị số bộ lọc đang áp dụng
- ✅ Message "Đã áp dụng bộ lọc" xuất hiện
