import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Descriptions,
  Tag,
  Tabs,
  Timeline,
  Statistic,
  Divider,
  Modal,
  Form,
  InputNumber,
  Input,
  Select,
  message,
  Spin,
  Alert
} from 'antd';
import {
  ArrowLeftOutlined,
  DollarOutlined,
  FileTextOutlined,
  CalendarOutlined,
  UserOutlined,
  ShoppingCartOutlined,
  HistoryOutlined,
  EditOutlined,
  PlusOutlined,
  MessageOutlined,
  PhoneOutlined,
  BellOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import dayjs from 'dayjs';
import { testAPI } from '../../services/api';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;

// Hook để lấy chi tiết công nợ khách hàng
const useCustomerDebtDetail = (customerId) => {
  return useQuery(
    ['customer-debt-detail', customerId],
    async () => {
      const response = await testAPI.getCustomerDebtDetail(customerId);
      return response;
    },
    {
      enabled: !!customerId,
      refetchOnWindowFocus: false
    }
  );
};

// Hook để tạo phiếu thu
const useCreatePayment = () => {
  const queryClient = useQueryClient();

  return useMutation(
    async (paymentData) => {
      const response = await testAPI.createPayment(paymentData);
      return response;
    },
    {
      onSuccess: () => {
        message.success('Tạo phiếu thu thành công!');
        queryClient.invalidateQueries(['customer-debt-detail']);
      },
      onError: (error) => {
        message.error(`Lỗi: ${error.message}`);
      }
    }
  );
};

// Hook để lấy ghi chú nội bộ
const useInternalNotes = (customerId) => {
  return useQuery(
    ['internal-notes', customerId],
    async () => {
      const response = await testAPI.getInternalNotes(customerId);
      return response;
    },
    {
      enabled: !!customerId,
      refetchOnWindowFocus: false
    }
  );
};

// Hook để tạo ghi chú nội bộ
const useCreateInternalNote = () => {
  const queryClient = useQueryClient();

  return useMutation(
    async (noteData) => {
      const response = await testAPI.createInternalNote(noteData);
      return response;
    },
    {
      onSuccess: () => {
        message.success('Tạo ghi chú thành công!');
        queryClient.invalidateQueries(['internal-notes']);
      },
      onError: (error) => {
        message.error(`Lỗi: ${error.message}`);
      }
    }
  );
};

const DebtDetail = () => {
  const { customerId } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [noteForm] = Form.useForm();
  const [paymentModal, setPaymentModal] = useState(false);
  const [noteModal, setNoteModal] = useState(false);
  const { data, isLoading, error } = useCustomerDebtDetail(customerId);
  const { data: notesData, isLoading: notesLoading } = useInternalNotes(customerId);
  const createPaymentMutation = useCreatePayment();
  const createNoteMutation = useCreateInternalNote();

  if (isLoading || notesLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Lỗi"
        description={error.message}
        type="error"
        showIcon
        style={{ margin: '20px' }}
      />
    );
  }

  const customerData = data?.data;
  const internalNotes = notesData?.data || [];

  if (!customerData) {
    return (
      <Alert
        message="Không tìm thấy thông tin khách hàng"
        type="warning"
        showIcon
        style={{ margin: '20px' }}
      />
    );
  }

  // Cấu hình cột cho bảng đơn hàng
  const orderColumns = [
    {
      title: 'Mã đơn hàng',
      dataIndex: 'order_code',
      key: 'order_code',
      render: (text) => <Text strong>{text}</Text>
    },
    {
      title: 'Ngày đặt',
      dataIndex: 'order_date',
      key: 'order_date',
      render: (date) => dayjs(date).format('DD/MM/YYYY')
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'total_amount',
      key: 'total_amount',
      align: 'right',
      render: (value) => new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(value)
    },
    {
      title: 'Đã trả',
      dataIndex: 'paid_amount',
      key: 'paid_amount',
      align: 'right',
      render: (value) => new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(value)
    },
    {
      title: 'Còn nợ',
      dataIndex: 'debt_amount',
      key: 'debt_amount',
      align: 'right',
      render: (value) => (
        <Text strong style={{ color: value > 0 ? '#ff4d4f' : '#52c41a' }}>
          {new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
          }).format(value)}
        </Text>
      )
    },
    {
      title: 'Trạng thái',
      dataIndex: 'payment_status',
      key: 'payment_status',
      render: (status) => {
        const statusConfig = {
          'paid': { color: 'green', text: 'Đã thanh toán' },
          'partial': { color: 'orange', text: 'Thanh toán một phần' },
          'unpaid': { color: 'red', text: 'Chưa thanh toán' }
        };
        const config = statusConfig[status] || statusConfig.unpaid;
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    }
  ];

  // Cấu hình cột cho bảng lịch sử thanh toán
  const paymentColumns = [
    {
      title: 'Ngày thanh toán',
      dataIndex: 'payment_date',
      key: 'payment_date',
      render: (date) => dayjs(date).format('DD/MM/YYYY HH:mm')
    },
    {
      title: 'Số tiền',
      dataIndex: 'amount',
      key: 'amount',
      align: 'right',
      render: (value) => (
        <Text strong style={{ color: '#52c41a' }}>
          {new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
          }).format(value)}
        </Text>
      )
    },
    {
      title: 'Phương thức',
      dataIndex: 'payment_method',
      key: 'payment_method'
    },
    {
      title: 'Ghi chú',
      dataIndex: 'note',
      key: 'note'
    },
    {
      title: 'Người tạo',
      dataIndex: 'created_by',
      key: 'created_by'
    }
  ];

  // Xử lý tạo phiếu thu
  const handleCreatePayment = () => {
    setPaymentModal(true);
    form.resetFields();
    form.setFieldsValue({
      customer_id: customerId,
      amount: customerData.total_debt
    });
  };

  // Xử lý submit phiếu thu
  const handlePaymentSubmit = async (values) => {
    try {
      await createPaymentMutation.mutateAsync({
        ...values,
        customer_id: customerId,
        type: 'thu'
      });
      setPaymentModal(false);
      form.resetFields();
    } catch (error) {
      console.error('Error creating payment:', error);
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/accounting/debt')}
          >
            Quay lại
          </Button>
          <Title level={3} style={{ margin: 0 }}>
            Chi tiết công nợ - {customerData.customer_name}
          </Title>
        </Space>
        
        <div style={{ float: 'right' }}>
          <Space>
            <Button
              icon={<MessageOutlined />}
              onClick={() => setNoteModal(true)}
            >
              Ghi chú nội bộ
            </Button>
            <Button
              type="primary"
              icon={<DollarOutlined />}
              onClick={handleCreatePayment}
              disabled={customerData.total_debt <= 0}
            >
              Thu tiền
            </Button>
          </Space>
        </div>
        <div style={{ clear: 'both' }} />
      </div>

      {/* Customer Info & Statistics */}
      <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
        <Col span={16}>
          <Card title="Thông tin khách hàng">
            <Descriptions column={2} bordered>
              <Descriptions.Item label="Tên khách hàng" span={1}>
                <Text strong>{customerData.customer_name}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="Số điện thoại" span={1}>
                {customerData.customer_phone}
              </Descriptions.Item>
              <Descriptions.Item label="Email" span={1}>
                {customerData.customer_email || 'Chưa có'}
              </Descriptions.Item>
              <Descriptions.Item label="Địa chỉ" span={1}>
                {customerData.customer_address || 'Chưa có'}
              </Descriptions.Item>
              <Descriptions.Item label="Ngày tạo" span={1}>
                {dayjs(customerData.created_date).format('DD/MM/YYYY')}
              </Descriptions.Item>
              <Descriptions.Item label="Lần mua cuối" span={1}>
                {customerData.last_order_date ? 
                  dayjs(customerData.last_order_date).format('DD/MM/YYYY') : 
                  'Chưa có đơn hàng'
                }
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        <Col span={8}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card>
                <Statistic
                  title="Tổng công nợ"
                  value={customerData.total_debt}
                  formatter={(value) => new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                  }).format(value)}
                  valueStyle={{ color: customerData.total_debt > 0 ? '#ff4d4f' : '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={24}>
              <Card>
                <Statistic
                  title="Tổng đã mua"
                  value={customerData.total_purchased}
                  formatter={(value) => new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                  }).format(value)}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
          </Row>
        </Col>
      </Row>

      {/* Tabs for Orders and Payment History */}
      <Card>
        <Tabs defaultActiveKey="orders">
          <TabPane 
            tab={
              <span>
                <ShoppingCartOutlined />
                Đơn hàng ({customerData.orders?.length || 0})
              </span>
            } 
            key="orders"
          >
            <Table
              columns={orderColumns}
              dataSource={customerData.orders || []}
              rowKey="order_id"
              pagination={false}
              size="small"
            />
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <HistoryOutlined />
                Lịch sử thanh toán ({customerData.payments?.length || 0})
              </span>
            } 
            key="payments"
          >
            <Table
              columns={paymentColumns}
              dataSource={customerData.payments || []}
              rowKey="payment_id"
              pagination={false}
              size="small"
            />
          </TabPane>

          <TabPane
            tab={
              <span>
                <CalendarOutlined />
                Timeline
              </span>
            }
            key="timeline"
          >
            <Timeline>
              {customerData.timeline?.map((item, index) => (
                <Timeline.Item
                  key={index}
                  color={item.type === 'order' ? 'blue' : 'green'}
                >
                  <div>
                    <Text strong>{item.title}</Text>
                    <br />
                    <Text type="secondary">{dayjs(item.date).format('DD/MM/YYYY HH:mm')}</Text>
                    <br />
                    <Text>{item.description}</Text>
                  </div>
                </Timeline.Item>
              )) || []}
            </Timeline>
          </TabPane>

          <TabPane
            tab={
              <span>
                <MessageOutlined />
                Ghi chú nội bộ ({internalNotes.length})
              </span>
            }
            key="notes"
          >
            <div style={{ marginBottom: '16px' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setNoteModal(true)}
              >
                Thêm ghi chú
              </Button>
            </div>

            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {internalNotes.map(note => (
                <Card
                  key={note.id}
                  size="small"
                  style={{ marginBottom: '12px' }}
                  extra={
                    <Space>
                      {note.type === 'reminder' && <BellOutlined style={{ color: '#faad14' }} />}
                      {note.type === 'contact' && <PhoneOutlined style={{ color: '#52c41a' }} />}
                      <Button type="text" size="small" icon={<EditOutlined />} />
                    </Space>
                  }
                >
                  <div>
                    <Text>{note.content}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {note.created_by} - {note.created_at}
                    </Text>
                  </div>
                </Card>
              ))}
            </div>
          </TabPane>
        </Tabs>
      </Card>

      {/* Payment Modal */}
      <Modal
        title="Tạo phiếu thu tiền"
        open={paymentModal}
        onCancel={() => setPaymentModal(false)}
        footer={null}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handlePaymentSubmit}
        >
          <Form.Item
            label="Số tiền thu"
            name="amount"
            rules={[
              { required: true, message: 'Vui lòng nhập số tiền!' },
              { type: 'number', min: 1, message: 'Số tiền phải lớn hơn 0!' }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/\$\s?|(,*)/g, '')}
              placeholder="Nhập số tiền thu"
            />
          </Form.Item>

          <Form.Item
            label="Phương thức thanh toán"
            name="payment_method"
            rules={[{ required: true, message: 'Vui lòng chọn phương thức!' }]}
          >
            <Select placeholder="Chọn phương thức thanh toán">
              <Option value="cash">💵 Tiền mặt</Option>
              <Option value="transfer">🏦 Chuyển khoản</Option>
              <Option value="card">💳 Thẻ tín dụng/ghi nợ</Option>
              <Option value="check">📝 Séc</Option>
              <Option value="other">🔄 Khác</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="Ghi chú"
            name="note"
          >
            <TextArea rows={3} placeholder="Ghi chú về phiếu thu..." />
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setPaymentModal(false)}>
                Hủy
              </Button>
              <Button 
                type="primary" 
                htmlType="submit"
                loading={createPaymentMutation.isLoading}
              >
                Tạo phiếu thu
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Internal Notes Modal */}
      <Modal
        title="Thêm ghi chú nội bộ"
        open={noteModal}
        onCancel={() => setNoteModal(false)}
        footer={null}
        width={600}
      >
        <Form
          form={noteForm}
          layout="vertical"
          onFinish={async (values) => {
            try {
              await createNoteMutation.mutateAsync({
                customer_id: customerId,
                content: values.content,
                type: values.type
              });
              setNoteModal(false);
              noteForm.resetFields();
            } catch (error) {
              console.error('Error creating note:', error);
            }
          }}
        >
          <Form.Item
            label="Loại ghi chú"
            name="type"
            rules={[{ required: true, message: 'Vui lòng chọn loại ghi chú!' }]}
          >
            <Select placeholder="Chọn loại ghi chú">
              <Option value="reminder">
                <BellOutlined style={{ marginRight: '8px', color: '#faad14' }} />
                Nhắc nhở
              </Option>
              <Option value="contact">
                <PhoneOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
                Liên hệ
              </Option>
              <Option value="meeting">
                <UserOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                Cuộc họp
              </Option>
              <Option value="general">
                <MessageOutlined style={{ marginRight: '8px', color: '#666' }} />
                Ghi chú chung
              </Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="Nội dung ghi chú"
            name="content"
            rules={[{ required: true, message: 'Vui lòng nhập nội dung!' }]}
          >
            <TextArea
              rows={4}
              placeholder="Nhập nội dung ghi chú nội bộ..."
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setNoteModal(false)}>
                Hủy
              </Button>
              <Button type="primary" htmlType="submit">
                Lưu ghi chú
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DebtDetail;
