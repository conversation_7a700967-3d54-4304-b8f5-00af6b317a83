const { getBusinessActivityReport } = require("./controllers/reportController");

// Mock request và response objects
const mockReq = {
  query: {
    // Không có startDate và endDate để sử dụng default (30 ngày gần đây)
    limit: 5,
  },
};

const mockRes = {
  json: function (data) {
    console.log("📊 Controller Response:");
    console.log("- success:", data.success);

    if (data.data?.businessActivity) {
      console.log("\n👥 Sample Business Activity Records:");
      data.data.businessActivity.slice(0, 3).forEach((customer, index) => {
        console.log(`${index + 1}. Customer ID: ${customer.customerId}`);
        console.log(`   Customer Name: "${customer.customerName}"`);
        console.log(`   Customer Group: ${customer.customerGroup}`);
        console.log(`   Order Count: ${customer.orderCount}`);
        console.log(`   Revenue: ${customer.revenue}`);
        console.log("   ---");
      });
    }

    console.log("\n✅ Controller test completed!");
    process.exit(0);
  },
  status: function (code) {
    console.log("Response status:", code);
    return this;
  },
};

async function testController() {
  try {
    console.log("🔍 Testing controller directly...");
    await getBusinessActivityReport(mockReq, mockRes);
  } catch (error) {
    console.error("❌ Controller Error:", error);
    process.exit(1);
  }
}

testController();
