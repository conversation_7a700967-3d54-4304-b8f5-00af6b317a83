const express = require("express");
const { asyncHandler } = require("../middleware/errorHandler");
const {
  getOverviewReport,
  getProductReport,
  getCustomerReport,
  getBusinessActivityReport,
  getCustomerGroupReport,
} = require("../controllers/reportController");

const router = express.Router();

/**
 * @route GET /api/reports/overview
 * @desc Lấy báo cáo tổng quan
 * @access Private
 * @query startDate, endDate, period
 */
router.get("/overview", asyncHandler(getOverviewReport));

/**
 * @route GET /api/reports/products
 * @desc Lấy báo cáo sản phẩm
 * @access Private
 * @query startDate, endDate, limit
 */
router.get("/products", asyncHandler(getProductReport));

/**
 * @route GET /api/reports/customers
 * @desc Lấy báo cáo khách hàng
 * @access Private
 * @query startDate, endDate, limit
 */
router.get("/customers", asyncHandler(getCustomerReport));

/**
 * @route GET /api/reports/business-activity
 * @desc Lấy báo cáo hoạt động kinh doanh theo khách hàng
 * @access Private
 * @query startDate, endDate, customerType, limit
 */
router.get("/business-activity", asyncHandler(getBusinessActivityReport));

/**
 * @route GET /api/reports/customer-groups
 * @desc Lấy báo cáo nhóm khách hàng
 * @access Private
 * @query startDate, endDate, limit
 */
router.get("/customer-groups", asyncHandler(getCustomerGroupReport));

module.exports = router;
