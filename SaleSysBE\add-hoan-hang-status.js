const { sequelize } = require('./models');

async function addHoanHangStatus() {
  try {
    console.log('🔧 Adding "hoan_hang" status to don_hang table...');

    // Thêm trạng thái 'hoan_hang' vào ENUM
    await sequelize.query(`
      ALTER TABLE don_hang 
      MODIFY COLUMN trang_thai 
      ENUM('cho_xu_ly', 'da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh', 'huy', 'hoan_hang') 
      DEFAULT 'cho_xu_ly'
    `);

    console.log('✅ Successfully added "hoan_hang" status to don_hang table');
    
    // Kiểm tra kết quả
    const [results] = await sequelize.query(`
      SHOW COLUMNS FROM don_hang WHERE Field = 'trang_thai'
    `);
    
    console.log('📋 Current trang_thai column definition:');
    console.log(results[0]);

  } catch (error) {
    console.error('❌ Error adding hoan_hang status:', error.message);
  } finally {
    await sequelize.close();
  }
}

addHoanHangStatus();
