import { useQuery } from "react-query";
import { filterDataAPI } from "../services/api";

// Hook để lấy dữ liệu nhóm khách hàng
export const useCustomerGroups = () => {
  return useQuery(
    ["customerGroups"],
    async () => {
      const response = await filterDataAPI.getCustomerGroups();
      return response.data;
    },
    {
      staleTime: 5 * 60 * 1000, // 5 phút
      cacheTime: 10 * 60 * 1000, // 10 phút
    }
  );
};

// Hook để lấy danh sách khách hàng
export const useCustomers = (params = {}) => {
  return useQuery(
    ["customers", params],
    async () => {
      const response = await filterDataAPI.getCustomers({
        page: 1,
        limit: 1000, // Lấy nhiều để có đủ dữ liệu cho filter
        ...params,
      });
      return response.data;
    },
    {
      staleTime: 2 * 60 * 1000, // 2 phút
      cacheTime: 5 * 60 * 1000, // 5 phút
    }
  );
};

// Hook để lấy dữ liệu danh mục sản phẩm
export const useProductCategories = () => {
  return useQuery(
    ["productCategories"],
    async () => {
      const response = await filterDataAPI.getProductCategories();
      return response.data;
    },
    {
      staleTime: 5 * 60 * 1000,
      cacheTime: 10 * 60 * 1000,
    }
  );
};

// Hook để lấy dữ liệu nhãn hiệu
export const useBrands = () => {
  return useQuery(
    ["brands"],
    async () => {
      const response = await filterDataAPI.getBrands();
      return response.data;
    },
    {
      staleTime: 5 * 60 * 1000,
      cacheTime: 10 * 60 * 1000,
    }
  );
};

// Hook để lấy dữ liệu tags
export const useTags = () => {
  return useQuery(
    ["tags"],
    async () => {
      const response = await filterDataAPI.getTags();
      return response.data;
    },
    {
      staleTime: 5 * 60 * 1000,
      cacheTime: 10 * 60 * 1000,
    }
  );
};

// Hook để lấy dữ liệu kho hàng
export const useWarehouses = () => {
  return useQuery(
    ["warehouses"],
    async () => {
      const response = await filterDataAPI.getWarehouses();
      return response.data;
    },
    {
      staleTime: 5 * 60 * 1000,
      cacheTime: 10 * 60 * 1000,
    }
  );
};

// Hook tổng hợp để lấy tất cả dữ liệu filter
export const useAllFilterData = () => {
  const customerGroups = useCustomerGroups();
  const customers = useCustomers();
  const productCategories = useProductCategories();
  const brands = useBrands();
  const tags = useTags();
  const warehouses = useWarehouses();

  return {
    customerGroups,
    customers,
    productCategories,
    brands,
    tags,
    warehouses,
    isLoading:
      customerGroups.isLoading ||
      customers.isLoading ||
      productCategories.isLoading ||
      brands.isLoading ||
      tags.isLoading ||
      warehouses.isLoading,
    isError:
      customerGroups.isError ||
      customers.isError ||
      productCategories.isError ||
      brands.isError ||
      tags.isError ||
      warehouses.isError,
  };
};
