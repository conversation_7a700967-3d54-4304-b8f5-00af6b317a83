const axios = require('axios');

async function testDebtReportAPI() {
  const baseURL = 'http://localhost:5000/api';
  
  try {
    console.log('🧪 Testing Debt Report API...\n');

    // 1. Test seed data endpoint
    console.log('1. Testing seed test data...');
    try {
      const seedResponse = await axios.post(`${baseURL}/debt/seed-test-data`);
      console.log('✅ Seed data response:', seedResponse.data);
    } catch (error) {
      console.log('⚠️ Seed data error (might be expected):', error.response?.data?.message || error.message);
    }

    // 2. Test debt report endpoint
    console.log('\n2. Testing debt report...');
    const reportResponse = await axios.get(`${baseURL}/debt/report`);
    console.log('✅ Debt report response:');
    console.log('📊 Stats:', reportResponse.data.data.stats);
    console.log('📈 Chart data length:', reportResponse.data.data.chartData?.length || 0);
    console.log('🥧 Pie data length:', reportResponse.data.data.pieData?.length || 0);
    console.log('👥 Top debtors length:', reportResponse.data.data.topDebtors?.length || 0);

    // 3. Test with date range
    console.log('\n3. Testing debt report with date range...');
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 3);
    const endDate = new Date();
    
    const reportWithDateResponse = await axios.get(`${baseURL}/debt/report`, {
      params: {
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate.toISOString().split('T')[0],
        type: 'overview'
      }
    });
    console.log('✅ Debt report with date range response:');
    console.log('📊 Stats:', reportWithDateResponse.data.data.stats);

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run the test
testDebtReportAPI();
