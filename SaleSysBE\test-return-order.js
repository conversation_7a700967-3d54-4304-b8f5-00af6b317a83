const axios = require('axios');

async function testReturnOrder() {
  const baseURL = 'http://localhost:5000/api';
  
  try {
    console.log('🧪 Testing Return Order Feature...\n');

    // 1. Tạo đơn hàng test
    console.log('1. Creating test order...');
    const orderData = {
      khach_hang_id: 20, // Sử dụng customer có sẵn
      ten_khach_hang: 'Nguyễn Việt Anh',
      so_dien_thoai: '0123456789',
      dia_chi: 'Test Address',
      ngay_dat_hang: new Date().toISOString(),
      trang_thai: 'cho_xu_ly',
      ghi_chu: 'Test order for return functionality',
      chi_tiet_don_hang: [
        {
          phien_ban_san_pham_id: 1,
          so_luong: 2,
          gia_ban: 100000,
          thanh_tien: 200000
        }
      ],
      tong_tien_hang: 200000,
      giam_gia: 0,
      tong_thanh_toan: 200000
    };

    const createResponse = await axios.post(`${baseURL}/orders`, orderData, {
      headers: {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json'
      }
    });

    const orderId = createResponse.data.data.id;
    console.log(`✅ Created order ID: ${orderId}`);

    // 2. Cập nhật trạng thái đơn hàng thành 'da_giao'
    console.log('\n2. Updating order status to "da_giao"...');
    await axios.put(`${baseURL}/orders/${orderId}/status`, {
      trang_thai: 'da_giao'
    }, {
      headers: {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json'
      }
    });
    console.log('✅ Order status updated to "da_giao"');

    // 3. Lấy thông tin tồn kho trước khi hoàn hàng
    console.log('\n3. Getting inventory before return...');
    try {
      const inventoryBefore = await axios.get(`${baseURL}/inventory`, {
        headers: { 'Authorization': 'Bearer test-token' }
      });
      console.log('📦 Inventory before return:', inventoryBefore.data.data?.slice(0, 3));
    } catch (error) {
      console.log('⚠️ Could not get inventory (endpoint might not exist)');
    }

    // 4. Hoàn hàng
    console.log('\n4. Processing return order...');
    const returnData = {
      ly_do_hoan_hang: 'Sản phẩm bị lỗi',
      ghi_chu_hoan_hang: 'Khách hàng phản ánh sản phẩm không đúng mô tả'
    };

    const returnResponse = await axios.post(`${baseURL}/orders/${orderId}/return`, returnData, {
      headers: {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Return order response:', returnResponse.data);

    // 5. Kiểm tra trạng thái đơn hàng sau khi hoàn
    console.log('\n5. Checking order status after return...');
    const orderAfterReturn = await axios.get(`${baseURL}/orders/${orderId}`, {
      headers: { 'Authorization': 'Bearer test-token' }
    });

    const order = orderAfterReturn.data.data;
    console.log(`📋 Order status after return: ${order.trang_thai}`);
    console.log(`📝 Order notes: ${order.ghi_chu}`);

    // 6. Lấy thông tin tồn kho sau khi hoàn hàng
    console.log('\n6. Getting inventory after return...');
    try {
      const inventoryAfter = await axios.get(`${baseURL}/inventory`, {
        headers: { 'Authorization': 'Bearer test-token' }
      });
      console.log('📦 Inventory after return:', inventoryAfter.data.data?.slice(0, 3));
    } catch (error) {
      console.log('⚠️ Could not get inventory (endpoint might not exist)');
    }

    // 7. Kiểm tra công nợ khách hàng
    console.log('\n7. Checking customer debt...');
    try {
      const debtResponse = await axios.get(`${baseURL}/debt/list`, {
        headers: { 'Authorization': 'Bearer test-token' }
      });
      const customerDebt = debtResponse.data.data?.find(d => d.khach_hang_id === 20);
      console.log('💰 Customer debt after return:', customerDebt);
    } catch (error) {
      console.log('⚠️ Could not get debt info:', error.response?.data?.message || error.message);
    }

    console.log('\n🎉 Return order test completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log(`   - Order ID: ${orderId}`);
    console.log(`   - Final Status: ${order.trang_thai}`);
    console.log(`   - Return Reason: ${returnData.ly_do_hoan_hang}`);
    console.log(`   - Products Returned: ${returnResponse.data.data?.returned_products || 'N/A'}`);
    console.log(`   - Debt Adjustment: ${returnResponse.data.data?.debt_adjustment || 0} VND`);

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.status === 401) {
      console.log('\n💡 Note: You need to be authenticated to run this test.');
      console.log('   Try running this test through the frontend or with a valid token.');
    }
  }
}

// Run the test
testReturnOrder();
