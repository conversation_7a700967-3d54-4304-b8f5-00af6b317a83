'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Thêm trạng thái 'hoan_hang' vào ENUM của cột trang_thai
    await queryInterface.sequelize.query(`
      ALTER TABLE don_hang 
      MODIFY COLUMN trang_thai 
      ENUM('cho_xu_ly', 'da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh', 'huy', 'hoan_hang') 
      DEFAULT 'cho_xu_ly'
    `);
  },

  down: async (queryInterface, Sequelize) => {
    // Xóa trạng thái 'hoan_hang' khỏi ENUM (rollback)
    await queryInterface.sequelize.query(`
      ALTER TABLE don_hang 
      MODIFY COLUMN trang_thai 
      ENUM('cho_xu_ly', 'da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh', 'huy') 
      DEFAULT 'cho_xu_ly'
    `);
  }
};
