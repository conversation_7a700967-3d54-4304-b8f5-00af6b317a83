import React, { useState } from "react";
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  DatePicker,
  Button,
  Space,
  Table,
  Tag,
  Progress,
  List,
  Avatar,
  Spin,
  Alert,
  Tabs,
  Select,
  Drawer,
  Form,
  Input,
  Checkbox,
  Divider,
  message,
} from "antd";
import {
  DollarOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  ShoppingOutlined,
  CalendarOutlined,
  DownloadOutlined,
  ReloadOutlined,
  FilterOutlined,
  ClearOutlined,
} from "@ant-design/icons";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  ComposedChart,
} from "recharts";
import dayjs from "dayjs";

import { useAuth } from "../../contexts/AuthContext";
import { usePermissions } from "../../contexts/PermissionContext";
import { useReportsData } from "../../hooks/useReports";
import { useAllFilterData } from "../../hooks/useFilterData";

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { Option } = Select;

const Reports = () => {
  const { user } = useAuth();
  const { canView } = usePermissions();

  // State cho filters
  const [dateRange, setDateRange] = useState([
    dayjs().subtract(30, "days"),
    dayjs(),
  ]);
  const [period, setPeriod] = useState("day");
  const [activeTab, setActiveTab] = useState("overview");
  const [reportType, setReportType] = useState("overview"); // Loại báo cáo

  // State cho bộ lọc nâng cao
  const [filterDrawerVisible, setFilterDrawerVisible] = useState(false);
  const [customerSearchText, setCustomerSearchText] = useState("");
  const [advancedFilters, setAdvancedFilters] = useState({
    customerGroups: [],
    customers: [], // Thêm danh sách khách hàng cụ thể
    customerTypes: [],
    productCategories: [],
    orderStatus: [],
    paymentMethods: [],
    salesChannels: [],
    regions: [],
    minOrderValue: null,
    maxOrderValue: null,
    minQuantity: null,
    maxQuantity: null,
  });
  const [form] = Form.useForm();

  // Hàm xử lý bộ lọc nâng cao - chỉ cập nhật form, không áp dụng ngay
  const handleFilterChange = (changedValues, allValues) => {
    // Không cập nhật advancedFilters ở đây để tránh auto-apply
    // Chỉ cập nhật khi user click "Áp dụng"
  };

  const handleApplyFilters = () => {
    const values = form.getFieldsValue();
    setAdvancedFilters(values);
    setFilterDrawerVisible(false);

    // Trigger refetch dữ liệu với bộ lọc mới
    setTimeout(() => {
      refetchAll();
    }, 100);

    message.success("Đã áp dụng bộ lọc");
  };

  const handleClearFilters = () => {
    const emptyFilters = {
      customerGroups: [],
      customers: [],
      customerTypes: [],
      productCategories: [],
      orderStatus: [],
      paymentMethods: [],
      salesChannels: [],
      regions: [],
      minOrderValue: null,
      maxOrderValue: null,
      minQuantity: null,
      maxQuantity: null,
    };
    setAdvancedFilters(emptyFilters);
    setCustomerSearchText("");
    form.resetFields();
    message.success("Đã xóa tất cả bộ lọc");
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    Object.entries(advancedFilters).forEach(([key, value]) => {
      if (Array.isArray(value) && value.length > 0) count++;
      if (typeof value === "number" && value !== null) count++;
      if (typeof value === "string" && value.trim() !== "") count++;
    });
    return count;
  };

  // Lấy dữ liệu báo cáo với bộ lọc nâng cao
  const reportParams = {
    startDate: dateRange[0]?.format("YYYY-MM-DD"),
    endDate: dateRange[1]?.format("YYYY-MM-DD"),
    period,
    // Thêm các bộ lọc nâng cao
    customerGroups: advancedFilters.customerGroups,
    customers: advancedFilters.customers,
    customerTypes: advancedFilters.customerTypes,
    productCategories: advancedFilters.productCategories,
    orderStatus: advancedFilters.orderStatus,
    paymentMethods: advancedFilters.paymentMethods,
    salesChannels: advancedFilters.salesChannels,
    minOrderValue: advancedFilters.minOrderValue,
    maxOrderValue: advancedFilters.maxOrderValue,
    minQuantity: advancedFilters.minQuantity,
    maxQuantity: advancedFilters.maxQuantity,
  };

  const {
    overview,
    products,
    customers,
    businessActivity,
    customerGroups,
    isLoading,
    hasError,
    refetchAll,
  } = useReportsData(reportParams);

  // Debug log để kiểm tra tham số báo cáo
  React.useEffect(() => {
    console.log("Report params:", reportParams);
  }, [reportParams]);

  // Debug log để kiểm tra dữ liệu business activity
  React.useEffect(() => {
    if (businessActivity.data?.businessActivity) {
      console.log(
        "Business Activity Data:",
        businessActivity.data.businessActivity.slice(0, 3)
      );
    }
  }, [businessActivity.data]);

  // Lấy dữ liệu cho bộ lọc
  const filterData = useAllFilterData();

  // Helper function để lấy danh sách khách hàng an toàn
  const getCustomersList = () => {
    const response = filterData.customers.data;
    if (!response) return [];

    // Kiểm tra các cấu trúc dữ liệu có thể có
    // Cấu trúc: { success: true, data: { customers: [...] } }
    if (
      response.data &&
      response.data.customers &&
      Array.isArray(response.data.customers)
    ) {
      return response.data.customers;
    }
    // Cấu trúc: { data: [...] }
    if (response.data && Array.isArray(response.data)) {
      return response.data;
    }
    // Cấu trúc: { customers: [...] }
    if (response.customers && Array.isArray(response.customers)) {
      return response.customers;
    }
    // Cấu trúc: [...]
    if (Array.isArray(response)) {
      return response;
    }

    return [];
  };

  // Debug log để kiểm tra cấu trúc dữ liệu
  React.useEffect(() => {
    if (filterData.customers.data) {
      console.log("Customer data structure:", filterData.customers.data);
      console.log("Customer list from helper:", getCustomersList());
    }
  }, [filterData.customers.data]);

  // Xử lý thay đổi date range
  const handleDateRangeChange = (dates) => {
    if (dates && dates.length === 2) {
      setDateRange(dates);
    }
  };

  // Xử lý export (placeholder)
  const handleExport = () => {
    console.log("Export report...");
    // TODO: Implement export functionality
  };

  // Loading state
  if (isLoading) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <Spin size="large" />
        <div style={{ marginTop: "20px" }}>Đang tải báo cáo...</div>
      </div>
    );
  }

  // Error state
  if (hasError) {
    return (
      <Alert
        message="Lỗi tải báo cáo"
        description="Không thể tải dữ liệu báo cáo. Vui lòng thử lại sau."
        type="error"
        showIcon
        action={
          <Button type="primary" onClick={refetchAll}>
            Thử lại
          </Button>
        }
        style={{ margin: "20px" }}
      />
    );
  }

  const overviewData = overview.data?.overview || {};
  const timeData = overview.data?.timeData || [];
  const topProducts = products.data?.topProducts || [];
  const categoryData = products.data?.categoryData || [];
  const topCustomers = customers.data?.topCustomers || [];
  const customerAnalysis = customers.data?.customerAnalysis || {};
  const customerGroupsData = customerGroups.data?.customerGroups || [];
  const groupSummary = customerGroups.data?.groupSummary || {};

  // Cấu hình columns cho bảng sản phẩm
  const productColumns = [
    {
      title: "Sản phẩm",
      dataIndex: "name",
      key: "name",
      width: "40%",
    },
    {
      title: "Số lượng bán",
      dataIndex: "quantity",
      key: "quantity",
      align: "right",
      render: (value) => value?.toLocaleString(),
    },
    {
      title: "Doanh thu",
      dataIndex: "revenue",
      key: "revenue",
      align: "right",
      render: (value) => `${value?.toLocaleString()}đ`,
    },
    {
      title: "Đơn hàng",
      dataIndex: "orders",
      key: "orders",
      align: "right",
    },
    {
      title: "Giá TB",
      dataIndex: "avgPrice",
      key: "avgPrice",
      align: "right",
      render: (value) => `${value?.toLocaleString()}đ`,
    },
  ];

  // Cấu hình columns cho bảng khách hàng
  const customerColumns = [
    {
      title: "Khách hàng",
      dataIndex: "name",
      key: "name",
      width: "30%",
    },
    {
      title: "SĐT",
      dataIndex: "phone",
      key: "phone",
      width: "20%",
    },
    {
      title: "Đơn hàng",
      dataIndex: "totalOrders",
      key: "totalOrders",
      align: "right",
    },
    {
      title: "Tổng chi tiêu",
      dataIndex: "totalSpent",
      key: "totalSpent",
      align: "right",
      render: (value) => `${value?.toLocaleString()}đ`,
    },
    {
      title: "Giá trị TB",
      dataIndex: "avgOrderValue",
      key: "avgOrderValue",
      align: "right",
      render: (value) => `${value?.toLocaleString()}đ`,
    },
    {
      title: "Đơn cuối",
      dataIndex: "lastOrderDate",
      key: "lastOrderDate",
      render: (value) => dayjs(value).format("DD/MM/YYYY"),
    },
  ];

  return (
    <div style={{ padding: "24px" }}>
      {/* Header */}
      <div style={{ marginBottom: "24px" }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={2} style={{ margin: 0 }}>
              📊 Báo cáo
            </Title>
            <Text type="secondary">Phân tích dữ liệu kinh doanh chi tiết</Text>
          </Col>
          <Col>
            <Space>
              <Select
                value={reportType}
                onChange={setReportType}
                style={{ width: 200 }}
                placeholder="Chọn loại báo cáo"
              >
                <Option value="overview">📊 Báo cáo tổng quan</Option>
                <Option value="business-activity">
                  📈 Báo cáo theo khách hàng
                </Option>
                <Option value="products">📦 Báo cáo theo sản phẩm</Option>
                <Option value="customers">
                  👥 Báo cáo theo nhóm khách hàng
                </Option>
                {/* <Option value="customer-groups">
                  👑 Báo cáo nhóm khách hàng
                </Option> */}
              </Select>
              <RangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
                format="DD/MM/YYYY"
                placeholder={["Từ ngày", "Đến ngày"]}
              />
              <Select
                value={period}
                onChange={setPeriod}
                style={{ width: 120 }}
              >
                <Option value="day">Theo ngày</Option>
                <Option value="week">Theo tuần</Option>
                <Option value="month">Theo tháng</Option>
              </Select>
              <Button icon={<ReloadOutlined />} onClick={refetchAll}>
                Làm mới
              </Button>
              <Button
                icon={<FilterOutlined />}
                onClick={() => setFilterDrawerVisible(true)}
              >
                Bộ lọc{" "}
                {getActiveFiltersCount() > 0 && (
                  <Tag color="blue" style={{ marginLeft: 4 }}>
                    {getActiveFiltersCount()}
                  </Tag>
                )}
              </Button>
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={handleExport}
              >
                Xuất báo cáo
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* Nội dung báo cáo dựa trên loại được chọn */}
      {reportType === "overview" && (
        <div>
          <Title level={3} style={{ marginBottom: 24 }}>
            📊 Báo cáo tổng quan
          </Title>
          {/* Thống kê tổng quan */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="Tổng doanh thu"
                  value={overviewData.totalRevenue || 0}
                  precision={0}
                  valueStyle={{ color: "#3f8600" }}
                  prefix={<DollarOutlined />}
                  suffix="đ"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="Tổng đơn hàng"
                  value={overviewData.totalOrders || 0}
                  valueStyle={{ color: "#1890ff" }}
                  prefix={<ShoppingCartOutlined />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="Tổng khách hàng"
                  value={overviewData.totalCustomers || 0}
                  valueStyle={{ color: "#722ed1" }}
                  prefix={<UserOutlined />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="Giá trị đơn TB"
                  value={overviewData.averageOrderValue || 0}
                  precision={0}
                  valueStyle={{ color: "#fa8c16" }}
                  prefix={<ShoppingOutlined />}
                  suffix="đ"
                />
              </Card>
            </Col>
          </Row>

          {/* Biểu đồ doanh thu theo thời gian */}
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="Doanh thu theo thời gian" style={{ height: 400 }}>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={timeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="dateDisplay" />
                    <YAxis />
                    <Tooltip
                      formatter={(value) => [
                        `${value.toLocaleString()} đ`,
                        "Doanh thu",
                      ]}
                      labelFormatter={(label) => `Ngày: ${label}`}
                    />
                    <Line
                      type="monotone"
                      dataKey="revenue"
                      stroke="#1890ff"
                      strokeWidth={2}
                      dot={{ fill: "#1890ff" }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Card>
            </Col>
          </Row>
        </div>
      )}

      {/* Báo cáo Báo cáo theo khách hàng */}
      {reportType === "business-activity" && (
        <div>
          <Title level={3} style={{ marginBottom: 24 }}>
            📈 Báo cáo theo khách hàng
          </Title>

          {/* Thống kê tổng quan */}
          {businessActivity.data?.summary && (
            <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
              <Col xs={24} sm={6}>
                <Card>
                  <Statistic
                    title="Tổng khách hàng"
                    value={businessActivity.data.summary.totalCustomers || 0}
                    valueStyle={{ color: "#722ed1" }}
                    prefix={<UserOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={6}>
                <Card>
                  <Statistic
                    title="Tổng đơn hàng"
                    value={businessActivity.data.summary.totalOrderCount || 0}
                    valueStyle={{ color: "#1890ff" }}
                    prefix={<ShoppingCartOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={6}>
                <Card>
                  <Statistic
                    title="Tổng doanh thu"
                    value={businessActivity.data.summary.totalRevenue || 0}
                    precision={0}
                    valueStyle={{ color: "#3f8600" }}
                    prefix={<DollarOutlined />}
                    suffix="đ"
                  />
                </Card>
              </Col>
              <Col xs={24} sm={6}>
                <Card>
                  <Statistic
                    title="Lợi nhuận gộp"
                    value={businessActivity.data.summary.totalGrossProfit || 0}
                    precision={0}
                    valueStyle={{ color: "#fa8c16" }}
                    prefix={<DollarOutlined />}
                    suffix="đ"
                  />
                </Card>
              </Col>
            </Row>
          )}

          {/* Bảng chi tiết Báo cáo theo khách hàng */}
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="Chi tiết Báo cáo theo khách hàng theo khách hàng">
                {businessActivity.isLoading ? (
                  <div style={{ textAlign: "center", padding: "50px" }}>
                    <Spin size="large" />
                  </div>
                ) : (
                  <Table
                    columns={[
                      {
                        title: "Tên khách hàng",
                        dataIndex: "customerName",
                        key: "customerName",
                        width: "15%",
                        render: (text, record) => (
                          <div>
                            <div style={{ fontWeight: "bold" }}>{text}</div>
                            {record.customerGroup && (
                              <div
                                style={{ fontSize: "12px", color: "#8c8c8c" }}
                              >
                                {record.customerGroup}
                              </div>
                            )}
                          </div>
                        ),
                      },
                      {
                        title: "SL hàng bán ra",
                        dataIndex: "quantitySold",
                        key: "quantitySold",
                        align: "right",
                        width: "10%",
                        render: (value) => value?.toLocaleString(),
                      },
                      {
                        title: "SL hàng thực bán",
                        dataIndex: "actualQuantitySold",
                        key: "actualQuantitySold",
                        align: "right",
                        width: "10%",
                        render: (value) => value?.toLocaleString(),
                      },
                      {
                        title: "SL đơn hàng",
                        dataIndex: "orderCount",
                        key: "orderCount",
                        align: "right",
                        width: "8%",
                      },
                      {
                        title: "Tiền hàng",
                        dataIndex: "totalAmount",
                        key: "totalAmount",
                        align: "right",
                        width: "12%",
                        render: (value) => `${value?.toLocaleString()}đ`,
                      },
                      {
                        title: "Tiền hàng trả lại",
                        dataIndex: "returnedAmount",
                        key: "returnedAmount",
                        align: "right",
                        width: "12%",
                        render: (value) =>
                          value > 0 ? (
                            <span style={{ color: "#ff4d4f" }}>
                              -{value?.toLocaleString()}đ
                            </span>
                          ) : (
                            "0đ"
                          ),
                      },
                      {
                        title: "Chiết khấu",
                        dataIndex: "discountAmount",
                        key: "discountAmount",
                        align: "right",
                        width: "10%",
                        render: (value) =>
                          value > 0 ? (
                            <span style={{ color: "#fa8c16" }}>
                              -{value?.toLocaleString()}đ
                            </span>
                          ) : (
                            "0đ"
                          ),
                      },
                      {
                        title: "Phí giao hàng",
                        dataIndex: "shippingFee",
                        key: "shippingFee",
                        align: "right",
                        width: "10%",
                        render: (value) => `${value?.toLocaleString()}đ`,
                      },
                      {
                        title: "Doanh thu",
                        dataIndex: "revenue",
                        key: "revenue",
                        align: "right",
                        width: "12%",
                        render: (value) => (
                          <span
                            style={{ fontWeight: "bold", color: "#3f8600" }}
                          >
                            {value?.toLocaleString()}đ
                          </span>
                        ),
                      },
                      {
                        title: "Lợi nhuận gộp",
                        dataIndex: "grossProfit",
                        key: "grossProfit",
                        align: "right",
                        width: "12%",
                        render: (value) => (
                          <span
                            style={{
                              fontWeight: "bold",
                              color: value >= 0 ? "#3f8600" : "#ff4d4f",
                            }}
                          >
                            {value >= 0 ? "+" : ""}
                            {value?.toLocaleString()}đ
                          </span>
                        ),
                      },
                    ]}
                    dataSource={
                      businessActivity.data?.businessActivity?.map(
                        (item, index) => ({
                          ...item,
                          key: item.customerId || index,
                        })
                      ) || []
                    }
                    pagination={{
                      pageSize: 20,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total, range) =>
                        `${range[0]}-${range[1]} của ${total} khách hàng`,
                    }}
                    size="small"
                    scroll={{ x: 1200 }}
                    summary={(pageData) => {
                      const totalQuantitySold = pageData.reduce(
                        (sum, record) => sum + (record.quantitySold || 0),
                        0
                      );
                      const totalActualQuantitySold = pageData.reduce(
                        (sum, record) => sum + (record.actualQuantitySold || 0),
                        0
                      );
                      const totalOrderCount = pageData.reduce(
                        (sum, record) => sum + (record.orderCount || 0),
                        0
                      );
                      const totalAmount = pageData.reduce(
                        (sum, record) => sum + (record.totalAmount || 0),
                        0
                      );
                      const totalReturnedAmount = pageData.reduce(
                        (sum, record) => sum + (record.returnedAmount || 0),
                        0
                      );
                      const totalDiscountAmount = pageData.reduce(
                        (sum, record) => sum + (record.discountAmount || 0),
                        0
                      );
                      const totalShippingFee = pageData.reduce(
                        (sum, record) => sum + (record.shippingFee || 0),
                        0
                      );
                      const totalRevenue = pageData.reduce(
                        (sum, record) => sum + (record.revenue || 0),
                        0
                      );
                      const totalGrossProfit = pageData.reduce(
                        (sum, record) => sum + (record.grossProfit || 0),
                        0
                      );

                      return (
                        <Table.Summary.Row
                          style={{
                            backgroundColor: "#fafafa",
                            fontWeight: "bold",
                          }}
                        >
                          <Table.Summary.Cell index={0}>
                            Tổng
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={1} align="right">
                            {totalQuantitySold.toLocaleString()}
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={2} align="right">
                            {totalActualQuantitySold.toLocaleString()}
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={3} align="right">
                            {totalOrderCount.toLocaleString()}
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={4} align="right">
                            {totalAmount.toLocaleString()}đ
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={5} align="right">
                            <span style={{ color: "#ff4d4f" }}>
                              -{totalReturnedAmount.toLocaleString()}đ
                            </span>
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={6} align="right">
                            <span style={{ color: "#fa8c16" }}>
                              -{totalDiscountAmount.toLocaleString()}đ
                            </span>
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={7} align="right">
                            {totalShippingFee.toLocaleString()}đ
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={8} align="right">
                            <span style={{ color: "#3f8600" }}>
                              {totalRevenue.toLocaleString()}đ
                            </span>
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={9} align="right">
                            <span
                              style={{
                                color:
                                  totalGrossProfit >= 0 ? "#3f8600" : "#ff4d4f",
                              }}
                            >
                              {totalGrossProfit >= 0 ? "+" : ""}
                              {totalGrossProfit.toLocaleString()}đ
                            </span>
                          </Table.Summary.Cell>
                        </Table.Summary.Row>
                      );
                    }}
                  />
                )}
              </Card>
            </Col>
          </Row>
        </div>
      )}

      {/* Báo cáo sản phẩm */}
      {reportType === "products" && (
        <div>
          <Title level={3} style={{ marginBottom: 24 }}>
            📦 Báo cáo sản phẩm
          </Title>

          {/* Bảng chi tiết sản phẩm */}
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="Chi tiết Báo cáo theo khách hàng theo sản phẩm">
                {products.isLoading ? (
                  <div style={{ textAlign: "center", padding: "50px" }}>
                    <Spin size="large" />
                  </div>
                ) : (
                  <Table
                    columns={[
                      {
                        title: "Tên sản phẩm",
                        dataIndex: "name",
                        key: "name",
                        width: "25%",
                        render: (text, record) => (
                          <div>
                            <div
                              style={{ fontWeight: "bold", color: "#1890ff" }}
                            >
                              {text}
                            </div>
                            {record.sku && (
                              <div
                                style={{ fontSize: "12px", color: "#8c8c8c" }}
                              >
                                SKU: {record.sku}
                              </div>
                            )}
                          </div>
                        ),
                      },
                      {
                        title: "Mã SKU",
                        dataIndex: "sku",
                        key: "sku",
                        width: "12%",
                        render: (text) => (
                          <span
                            style={{
                              fontFamily: "monospace",
                              fontSize: "12px",
                            }}
                          >
                            {text}
                          </span>
                        ),
                      },
                      {
                        title: "SL đơn hàng",
                        dataIndex: "orders",
                        key: "orders",
                        align: "right",
                        width: "10%",
                      },
                      {
                        title: "Tiền hàng",
                        dataIndex: "totalAmount",
                        key: "totalAmount",
                        align: "right",
                        width: "12%",
                        render: (value) => `${(value || 0).toLocaleString()}đ`,
                      },
                      {
                        title: "Tiền hàng trả lại",
                        dataIndex: "returnedAmount",
                        key: "returnedAmount",
                        align: "right",
                        width: "12%",
                        render: (value) =>
                          value > 0 ? (
                            <span style={{ color: "#ff4d4f" }}>
                              -{value?.toLocaleString()}đ
                            </span>
                          ) : (
                            "0đ"
                          ),
                      },
                      {
                        title: "Tiền thuế",
                        dataIndex: "taxAmount",
                        key: "taxAmount",
                        align: "right",
                        width: "10%",
                        render: () => "0đ", // Placeholder
                      },
                      {
                        title: "Doanh thu",
                        dataIndex: "revenue",
                        key: "revenue",
                        align: "right",
                        width: "12%",
                        render: (value) => (
                          <span
                            style={{ fontWeight: "bold", color: "#3f8600" }}
                          >
                            {(value || 0).toLocaleString()}đ
                          </span>
                        ),
                      },
                      {
                        title: "Lợi nhuận gộp",
                        dataIndex: "grossProfit",
                        key: "grossProfit",
                        align: "right",
                        width: "12%",
                        render: (value) => {
                          const profit = value || 0;
                          return (
                            <span
                              style={{
                                fontWeight: "bold",
                                color: profit >= 0 ? "#3f8600" : "#ff4d4f",
                              }}
                            >
                              {profit >= 0 ? "+" : ""}
                              {profit.toLocaleString()}đ
                            </span>
                          );
                        },
                      },
                    ]}
                    dataSource={topProducts.map((item, index) => ({
                      ...item,
                      key: item.id || index,
                      // Thêm các trường cần thiết
                      sku:
                        item.sku || `SKU${String(index + 1).padStart(3, "0")}`,
                      totalAmount: item.totalAmount || item.revenue || 0,
                      returnedAmount: item.returnedAmount || 0,
                      taxAmount: 0, // Placeholder
                      grossProfit: item.grossProfit || item.revenue * 0.25, // Ước tính 25% lợi nhuận cho sản phẩm
                    }))}
                    pagination={{
                      pageSize: 20,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total, range) =>
                        `${range[0]}-${range[1]} của ${total} sản phẩm`,
                    }}
                    size="small"
                    scroll={{ x: 1000 }}
                    summary={(pageData) => {
                      const totalOrders = pageData.reduce(
                        (sum, record) => sum + (record.orders || 0),
                        0
                      );
                      const totalAmount = pageData.reduce(
                        (sum, record) => sum + (record.totalAmount || 0),
                        0
                      );
                      const totalReturnedAmount = pageData.reduce(
                        (sum, record) => sum + (record.returnedAmount || 0),
                        0
                      );
                      const totalRevenue = pageData.reduce(
                        (sum, record) => sum + (record.revenue || 0),
                        0
                      );
                      const totalGrossProfit = pageData.reduce(
                        (sum, record) => sum + (record.grossProfit || 0),
                        0
                      );

                      return (
                        <Table.Summary.Row
                          style={{
                            backgroundColor: "#fafafa",
                            fontWeight: "bold",
                          }}
                        >
                          <Table.Summary.Cell index={0}>
                            Tổng
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={1}></Table.Summary.Cell>
                          <Table.Summary.Cell index={2} align="right">
                            {totalOrders.toLocaleString()}
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={3} align="right">
                            {totalAmount.toLocaleString()}đ
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={4} align="right">
                            <span style={{ color: "#ff4d4f" }}>
                              -{totalReturnedAmount.toLocaleString()}đ
                            </span>
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={5} align="right">
                            0đ
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={6} align="right">
                            <span style={{ color: "#3f8600" }}>
                              {totalRevenue.toLocaleString()}đ
                            </span>
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={7} align="right">
                            <span
                              style={{
                                color:
                                  totalGrossProfit >= 0 ? "#3f8600" : "#ff4d4f",
                              }}
                            >
                              {totalGrossProfit >= 0 ? "+" : ""}
                              {totalGrossProfit.toLocaleString()}đ
                            </span>
                          </Table.Summary.Cell>
                        </Table.Summary.Row>
                      );
                    }}
                  />
                )}
              </Card>
            </Col>
          </Row>
        </div>
      )}

      {/* Báo cáo khách hàng */}
      {reportType === "customers" && (
        <div>
          <Title level={3} style={{ marginBottom: 24 }}>
            👥 Báo cáo khách hàng
          </Title>

          {/* Biểu đồ cột doanh thu theo nhóm khách hàng */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col span={24}>
              <Card
                title="Doanh thu theo nhóm khách hàng"
                style={{ height: 500 }}
              >
                {customerGroups.isLoading ? (
                  <div style={{ textAlign: "center", padding: "50px" }}>
                    <Spin size="large" />
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height={400}>
                    <ComposedChart
                      data={customerGroupsData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="groupName"
                        angle={-45}
                        textAnchor="end"
                        height={80}
                        interval={0}
                      />
                      <YAxis
                        yAxisId="left"
                        tickFormatter={(value) =>
                          `${(value / 1000000).toFixed(0)}M`
                        }
                      />
                      <YAxis
                        yAxisId="right"
                        orientation="right"
                        tickFormatter={(value) =>
                          `${(value / 1000000).toFixed(0)}M`
                        }
                      />
                      <Tooltip
                        formatter={(value, name) => {
                          if (name === "Doanh thu") {
                            return [`${value.toLocaleString()}đ`, name];
                          }
                          if (name === "Lợi nhuận gộp") {
                            return [`${value.toLocaleString()}đ`, name];
                          }
                          return [value, name];
                        }}
                        labelFormatter={(label) => `Nhóm: ${label}`}
                      />
                      <Bar
                        yAxisId="left"
                        dataKey="revenue"
                        fill="#1890ff"
                        name="Doanh thu"
                        radius={[4, 4, 0, 0]}
                      />
                      <Line
                        yAxisId="right"
                        type="monotone"
                        dataKey="grossProfit"
                        stroke="#52c41a"
                        strokeWidth={2}
                        name="Lợi nhuận gộp"
                        dot={{ fill: "#52c41a", strokeWidth: 2, r: 4 }}
                      />
                    </ComposedChart>
                  </ResponsiveContainer>
                )}
              </Card>
            </Col>
          </Row>

          {/* Bảng chi tiết nhóm khách hàng */}
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="Chi tiết theo nhóm khách hàng">
                {customerGroups.isLoading ? (
                  <div style={{ textAlign: "center", padding: "50px" }}>
                    <Spin size="large" />
                  </div>
                ) : (
                  <Table
                    columns={[
                      {
                        title: "Nhóm khách hàng",
                        dataIndex: "groupName",
                        key: "groupName",
                        width: "15%",
                        render: (text, record) => (
                          <div>
                            <span
                              style={{
                                color: record.color,
                                fontWeight: "bold",
                              }}
                            >
                              {text}
                            </span>
                            {record.groupCode && (
                              <div
                                style={{ fontSize: "12px", color: "#8c8c8c" }}
                              >
                                {record.groupCode}
                              </div>
                            )}
                          </div>
                        ),
                      },
                      {
                        title: "SL hàng bán ra",
                        dataIndex: "totalQuantitySold",
                        key: "totalQuantitySold",
                        align: "right",
                        width: "12%",
                        render: (value) => value?.toLocaleString() || 0,
                      },
                      {
                        title: "SL hàng thực bán",
                        dataIndex: "actualQuantitySold",
                        key: "actualQuantitySold",
                        align: "right",
                        width: "12%",
                        render: (value) => value?.toLocaleString() || 0,
                      },
                      {
                        title: "SL đơn hàng",
                        dataIndex: "totalOrders",
                        key: "totalOrders",
                        align: "right",
                        width: "10%",
                      },
                      {
                        title: "Tiền hàng",
                        dataIndex: "totalAmount",
                        key: "totalAmount",
                        align: "right",
                        width: "12%",
                        render: (value) => `${(value || 0).toLocaleString()}đ`,
                      },
                      {
                        title: "Tiền hàng trả lại",
                        dataIndex: "returnedAmount",
                        key: "returnedAmount",
                        align: "right",
                        width: "12%",
                        render: (value) =>
                          value > 0 ? (
                            <span style={{ color: "#ff4d4f" }}>
                              -{value?.toLocaleString()}đ
                            </span>
                          ) : (
                            "0đ"
                          ),
                      },
                      {
                        title: "Tiền thuế",
                        dataIndex: "taxAmount",
                        key: "taxAmount",
                        align: "right",
                        width: "10%",
                        render: () => "0đ", // Placeholder vì không có dữ liệu thuế
                      },
                      {
                        title: "Phí giao hàng",
                        dataIndex: "shippingFee",
                        key: "shippingFee",
                        align: "right",
                        width: "10%",
                        render: (value) => `${(value || 0).toLocaleString()}đ`,
                      },
                      {
                        title: "Doanh thu",
                        dataIndex: "revenue",
                        key: "revenue",
                        align: "right",
                        width: "12%",
                        render: (value) => (
                          <span
                            style={{ fontWeight: "bold", color: "#3f8600" }}
                          >
                            {(value || 0).toLocaleString()}đ
                          </span>
                        ),
                      },
                      {
                        title: "Lợi nhuận gộp",
                        dataIndex: "grossProfit",
                        key: "grossProfit",
                        align: "right",
                        width: "12%",
                        render: (value) => {
                          const profit = value || 0;
                          return (
                            <span
                              style={{
                                fontWeight: "bold",
                                color: profit >= 0 ? "#3f8600" : "#ff4d4f",
                              }}
                            >
                              {profit >= 0 ? "+" : ""}
                              {profit.toLocaleString()}đ
                            </span>
                          );
                        },
                      },
                    ]}
                    dataSource={customerGroupsData.map((item, index) => ({
                      ...item,
                      key: index,
                      // Dữ liệu từ API đã đầy đủ
                      totalQuantitySold: item.totalQuantitySold || 0,
                      actualQuantitySold: item.actualQuantitySold || 0,
                      totalAmount: item.totalAmount || 0,
                      returnedAmount: item.returnedAmount || 0,
                      taxAmount: 0, // Placeholder vì không có dữ liệu thuế
                      shippingFee: item.shippingFee || 0,
                      grossProfit: item.grossProfit || 0,
                    }))}
                    pagination={false}
                    size="small"
                    scroll={{ x: 1200 }}
                    summary={(pageData) => {
                      const totalQuantitySold = pageData.reduce(
                        (sum, record) => sum + (record.totalQuantitySold || 0),
                        0
                      );
                      const totalActualQuantitySold = pageData.reduce(
                        (sum, record) => sum + (record.actualQuantitySold || 0),
                        0
                      );
                      const totalOrderCount = pageData.reduce(
                        (sum, record) => sum + (record.totalOrders || 0),
                        0
                      );
                      const totalAmount = pageData.reduce(
                        (sum, record) => sum + (record.totalAmount || 0),
                        0
                      );
                      const totalReturnedAmount = pageData.reduce(
                        (sum, record) => sum + (record.returnedAmount || 0),
                        0
                      );
                      const totalShippingFee = pageData.reduce(
                        (sum, record) => sum + (record.shippingFee || 0),
                        0
                      );
                      const totalRevenue = pageData.reduce(
                        (sum, record) => sum + (record.revenue || 0),
                        0
                      );
                      const totalGrossProfit = pageData.reduce(
                        (sum, record) => sum + (record.grossProfit || 0),
                        0
                      );

                      return (
                        <Table.Summary.Row
                          style={{
                            backgroundColor: "#fafafa",
                            fontWeight: "bold",
                          }}
                        >
                          <Table.Summary.Cell index={0}>
                            Tổng
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={1} align="right">
                            {totalQuantitySold.toLocaleString()}
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={2} align="right">
                            {totalActualQuantitySold.toLocaleString()}
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={3} align="right">
                            {totalOrderCount.toLocaleString()}
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={4} align="right">
                            {totalAmount.toLocaleString()}đ
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={5} align="right">
                            <span style={{ color: "#ff4d4f" }}>
                              -{totalReturnedAmount.toLocaleString()}đ
                            </span>
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={6} align="right">
                            0đ
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={7} align="right">
                            {totalShippingFee.toLocaleString()}đ
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={8} align="right">
                            <span style={{ color: "#3f8600" }}>
                              {totalRevenue.toLocaleString()}đ
                            </span>
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={9} align="right">
                            <span
                              style={{
                                color:
                                  totalGrossProfit >= 0 ? "#3f8600" : "#ff4d4f",
                              }}
                            >
                              {totalGrossProfit >= 0 ? "+" : ""}
                              {totalGrossProfit.toLocaleString()}đ
                            </span>
                          </Table.Summary.Cell>
                        </Table.Summary.Row>
                      );
                    }}
                  />
                )}
              </Card>
            </Col>
          </Row>
        </div>
      )}

      {/* Báo cáo nhóm khách hàng */}
      {reportType === "customer-groups" && (
        <div>
          <Title level={3} style={{ marginBottom: 24 }}>
            👑 Báo cáo nhóm khách hàng
          </Title>

          {/* Thống kê tổng quan nhóm */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Tổng nhóm"
                  value={groupSummary.totalGroups || 0}
                  valueStyle={{ color: "#722ed1" }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Tổng khách hàng"
                  value={groupSummary.totalCustomers || 0}
                  valueStyle={{ color: "#1890ff" }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Tổng doanh thu"
                  value={groupSummary.totalRevenue || 0}
                  precision={0}
                  valueStyle={{ color: "#3f8600" }}
                  suffix="đ"
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Tổng đơn hàng"
                  value={groupSummary.totalOrders || 0}
                  valueStyle={{ color: "#fa8c16" }}
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} lg={16}>
              <Card title="Phân tích nhóm khách hàng">
                {customerGroups.isLoading ? (
                  <div style={{ textAlign: "center", padding: "50px" }}>
                    <Spin size="large" />
                  </div>
                ) : (
                  <Table
                    columns={[
                      {
                        title: "Nhóm khách hàng",
                        dataIndex: "groupName",
                        key: "groupName",
                        render: (text, record) => (
                          <div>
                            <span
                              style={{
                                color: record.color,
                                fontWeight: "bold",
                              }}
                            >
                              {text}
                            </span>
                            {record.groupCode && (
                              <div
                                style={{ fontSize: "12px", color: "#8c8c8c" }}
                              >
                                {record.groupCode}
                              </div>
                            )}
                          </div>
                        ),
                      },
                      {
                        title: "Số khách hàng",
                        dataIndex: "customerCount",
                        key: "customerCount",
                        align: "right",
                      },
                      {
                        title: "Doanh thu",
                        dataIndex: "revenue",
                        key: "revenue",
                        align: "right",
                        render: (value) => `${value?.toLocaleString()}đ`,
                      },
                      {
                        title: "Chi tiêu TB/KH",
                        dataIndex: "avgSpentPerCustomer",
                        key: "avgSpentPerCustomer",
                        align: "right",
                        render: (value) => `${value?.toLocaleString()}đ`,
                      },
                      {
                        title: "Tổng đơn hàng",
                        dataIndex: "totalOrders",
                        key: "totalOrders",
                        align: "right",
                      },
                    ]}
                    dataSource={customerGroupsData.map((item, index) => ({
                      ...item,
                      key: index,
                    }))}
                    pagination={false}
                    size="small"
                  />
                )}
              </Card>
            </Col>
            <Col xs={24} lg={8}>
              <Card title="Phân bố doanh thu theo nhóm" style={{ height: 400 }}>
                {customerGroups.isLoading ? (
                  <div style={{ textAlign: "center", padding: "50px" }}>
                    <Spin size="large" />
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={customerGroupsData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="revenue"
                        label={({ groupName, percent }) =>
                          `${groupName} ${(percent * 100).toFixed(0)}%`
                        }
                      >
                        {customerGroupsData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value) => [
                          `${value.toLocaleString()} đ`,
                          "Doanh thu",
                        ]}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </Card>
            </Col>
          </Row>
        </div>
      )}

      {/* Drawer bộ lọc nâng cao */}
      <Drawer
        title="Bộ lọc nâng cao"
        placement="right"
        width={400}
        onClose={() => setFilterDrawerVisible(false)}
        open={filterDrawerVisible}
        extra={
          <Space>
            <Button onClick={handleClearFilters} icon={<ClearOutlined />}>
              Xóa tất cả
            </Button>
            <Button type="primary" onClick={handleApplyFilters}>
              Áp dụng
            </Button>
          </Space>
        }
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={advancedFilters}
          onValuesChange={handleFilterChange}
        >
          {/* Nhóm khách hàng */}
          <Form.Item name="customerGroups" label="Nhóm khách hàng">
            <Select
              placeholder="Chọn nhóm khách hàng"
              allowClear
              loading={filterData.customerGroups.isLoading}
            >
              {filterData.customerGroups.data?.data?.map((group) => (
                <Option key={group.id} value={group.id}>
                  {group.ten_nhom} {group.ma_nhom && `(${group.ma_nhom})`}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* Tên khách hàng cụ thể với dropdown checkbox */}
          <Form.Item name="customers" label="Tên khách hàng">
            <Select
              mode="multiple"
              placeholder="Chọn tên khách hàng"
              allowClear
              showSearch
              filterOption={false}
              onSearch={setCustomerSearchText}
              searchValue={customerSearchText}
              loading={filterData.customers.isLoading}
              maxTagCount="responsive"
              tagRender={(props) => {
                const customer = getCustomersList().find(
                  (c) => c.id === props.value
                );
                const customerName =
                  customer?.ten || customer?.ho_ten || `ID: ${props.value}`;
                return (
                  <span
                    style={{
                      background: "#f0f0f0",
                      padding: "2px 8px",
                      borderRadius: "4px",
                      margin: "2px",
                      display: "inline-block",
                    }}
                  >
                    {customerName}
                    <span
                      onClick={props.onClose}
                      style={{
                        marginLeft: "4px",
                        cursor: "pointer",
                        color: "#999",
                      }}
                    >
                      ×
                    </span>
                  </span>
                );
              }}
              dropdownRender={(menu) => (
                <div style={{ padding: 8 }}>
                  <Input
                    placeholder="Tìm kiếm"
                    prefix={<span style={{ color: "#bfbfbf" }}>🔍</span>}
                    value={customerSearchText}
                    onChange={(e) => setCustomerSearchText(e.target.value)}
                    style={{ marginBottom: 8 }}
                  />
                  <div style={{ maxHeight: 200, overflowY: "auto" }}>
                    <Checkbox
                      indeterminate={
                        form.getFieldValue("customers")?.length > 0 &&
                        form.getFieldValue("customers")?.length <
                          getCustomersList().length
                      }
                      checked={
                        getCustomersList().length > 0 &&
                        form.getFieldValue("customers")?.length ===
                          getCustomersList().length
                      }
                      onChange={(e) => {
                        const allCustomerIds = getCustomersList().map(
                          (c) => c.id
                        );
                        const newCustomers = e.target.checked
                          ? allCustomerIds
                          : [];
                        // Chỉ cập nhật form, không cập nhật advancedFilters
                        form.setFieldsValue({ customers: newCustomers });
                      }}
                      style={{ marginBottom: 8, display: "block" }}
                    >
                      Chọn tất cả
                    </Checkbox>
                    <Checkbox.Group
                      value={form.getFieldValue("customers") || []}
                      onChange={(values) => {
                        // Chỉ cập nhật form, không cập nhật advancedFilters
                        form.setFieldsValue({ customers: values });
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          gap: 4,
                        }}
                      >
                        {getCustomersList()
                          .filter(
                            (customer) =>
                              !customerSearchText ||
                              customer.ten
                                ?.toLowerCase()
                                .includes(customerSearchText.toLowerCase()) ||
                              customer.ho_ten
                                ?.toLowerCase()
                                .includes(customerSearchText.toLowerCase()) ||
                              customer.ma_khach_hang
                                ?.toLowerCase()
                                .includes(customerSearchText.toLowerCase())
                          )
                          .map((customer) => (
                            <Checkbox key={customer.id} value={customer.id}>
                              {customer.ten ||
                                customer.ho_ten ||
                                "Không có tên"}{" "}
                              - {customer.ma_khach_hang || customer.id}
                            </Checkbox>
                          ))}
                      </div>
                    </Checkbox.Group>
                  </div>
                </div>
              )}
            >
              {/* Options sẽ được render bởi dropdownRender */}
            </Select>
          </Form.Item>

          {/* Loại khách hàng */}
          <Form.Item name="customerTypes" label="Chọn loại khách hàng">
            <Checkbox.Group>
              <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
                <Checkbox value="new">Khách hàng mới</Checkbox>
                <Checkbox value="returning">Khách hàng cũ</Checkbox>
                <Checkbox value="loyal">Khách hàng thân thiết</Checkbox>
              </div>
            </Checkbox.Group>
          </Form.Item>

          <Divider />

          {/* Danh mục sản phẩm */}
          <Form.Item name="productCategories" label="Chọn danh mục">
            <Select
              mode="multiple"
              placeholder="Chọn danh mục sản phẩm"
              allowClear
              loading={filterData.productCategories.isLoading}
            >
              {filterData.productCategories.data?.data?.map((category) => (
                <Option key={category.id} value={category.id}>
                  {category.ten}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* Trạng thái đơn hàng */}
          <Form.Item name="orderStatus" label="Trạng thái đơn hàng">
            <Checkbox.Group>
              <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
                <Checkbox value="da_xac_nhan">Đã xác nhận</Checkbox>
                <Checkbox value="da_dong_goi">Đã đóng gói</Checkbox>
                <Checkbox value="da_giao">Đã giao</Checkbox>
                <Checkbox value="hoan_thanh">Hoàn thành</Checkbox>
                <Checkbox value="huy">Đã hủy</Checkbox>
              </div>
            </Checkbox.Group>
          </Form.Item>

          <Divider />

          {/* Phương thức thanh toán */}
          <Form.Item name="paymentMethods" label="Phương thức thanh toán">
            <Checkbox.Group>
              <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
                <Checkbox value="cash">Tiền mặt</Checkbox>
                <Checkbox value="transfer">Chuyển khoản</Checkbox>
                <Checkbox value="card">Thẻ tín dụng</Checkbox>
                <Checkbox value="ewallet">Ví điện tử</Checkbox>
              </div>
            </Checkbox.Group>
          </Form.Item>

          {/* Kênh bán hàng */}
          <Form.Item name="salesChannels" label="Kênh bán hàng">
            <Checkbox.Group>
              <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
                <Checkbox value="online">Online</Checkbox>
                <Checkbox value="offline">Offline</Checkbox>
                <Checkbox value="mobile">Mobile App</Checkbox>
                <Checkbox value="social">Social Media</Checkbox>
              </div>
            </Checkbox.Group>
          </Form.Item>

          <Divider />

          {/* Giá trị đơn hàng */}
          <Form.Item label="Giá trị đơn hàng">
            <Input.Group compact>
              <Form.Item
                name="minOrderValue"
                style={{ width: "48%", marginBottom: 0 }}
              >
                <Input placeholder="Từ" type="number" addonAfter="đ" />
              </Form.Item>
              <span
                style={{ width: "4%", textAlign: "center", lineHeight: "32px" }}
              >
                -
              </span>
              <Form.Item
                name="maxOrderValue"
                style={{ width: "48%", marginBottom: 0 }}
              >
                <Input placeholder="Đến" type="number" addonAfter="đ" />
              </Form.Item>
            </Input.Group>
          </Form.Item>

          {/* Số lượng */}
          <Form.Item label="Số lượng sản phẩm">
            <Input.Group compact>
              <Form.Item
                name="minQuantity"
                style={{ width: "48%", marginBottom: 0 }}
              >
                <Input placeholder="Từ" type="number" />
              </Form.Item>
              <span
                style={{ width: "4%", textAlign: "center", lineHeight: "32px" }}
              >
                -
              </span>
              <Form.Item
                name="maxQuantity"
                style={{ width: "48%", marginBottom: 0 }}
              >
                <Input placeholder="Đến" type="number" />
              </Form.Item>
            </Input.Group>
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  );
};

export default Reports;
