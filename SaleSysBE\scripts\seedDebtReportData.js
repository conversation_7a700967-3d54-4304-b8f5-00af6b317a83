const { sequelize } = require('../models');
const { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>g<PERSON>o<PERSON>gu<PERSON>, 
  Payment,
  DonHang<PERSON>,
  PhienBanSanPham,
  SanPham
} = require('../models');

async function seedDebtReportData() {
  try {
    console.log('🌱 Seeding debt report data...');

    // 1. Tạo khách hàng test
    const customers = await NguoiDung.bulkCreate([
      {
        username: 'customer1',
        email: '<EMAIL>',
        ho_ten: 'Nguyễn Văn A',
        so_dien_thoai: '0901234567',
        dia_chi: 'H<PERSON> Nội',
        vai_tro: 'khach_hang',
        trang_thai: 'active'
      },
      {
        username: 'customer2',
        email: '<EMAIL>',
        ho_ten: 'Trần Thị B',
        so_dien_thoai: '0901234568',
        dia_chi: 'TP.HCM',
        vai_tro: 'khach_hang',
        trang_thai: 'active'
      },
      {
        username: 'customer3',
        email: '<EMAIL>',
        ho_ten: '<PERSON><PERSON> Văn C',
        so_dien_thoai: '0901234569',
        dia_chi: 'Đà Nẵng',
        vai_tro: 'khach_hang',
        trang_thai: 'active'
      },
      {
        username: 'customer4',
        email: '<EMAIL>',
        ho_ten: 'Phạm Thị D',
        so_dien_thoai: '0901234570',
        dia_chi: 'Cần Thơ',
        vai_tro: 'khach_hang',
        trang_thai: 'active'
      },
      {
        username: 'customer5',
        email: '<EMAIL>',
        ho_ten: 'Hoàng Văn E',
        so_dien_thoai: '0901234571',
        dia_chi: 'Hải Phòng',
        vai_tro: 'khach_hang',
        trang_thai: 'active'
      }
    ], { ignoreDuplicates: true });

    console.log(`✅ Created ${customers.length} customers`);

    // 2. Tạo đơn hàng test với các trạng thái khác nhau
    const orders = [];
    const orderData = [
      { customerId: 1, amount: 15000000, status: 'da_xac_nhan', daysAgo: 45 },
      { customerId: 1, amount: 5000000, status: 'hoan_thanh', daysAgo: 30 },
      { customerId: 2, amount: 12000000, status: 'da_dong_goi', daysAgo: 35 },
      { customerId: 2, amount: 3000000, status: 'hoan_thanh', daysAgo: 20 },
      { customerId: 3, amount: 10000000, status: 'da_giao', daysAgo: 40 },
      { customerId: 3, amount: 2000000, status: 'hoan_thanh', daysAgo: 15 },
      { customerId: 4, amount: 8000000, status: 'da_xac_nhan', daysAgo: 50 },
      { customerId: 4, amount: 1000000, status: 'hoan_thanh', daysAgo: 10 },
      { customerId: 5, amount: 7000000, status: 'da_dong_goi', daysAgo: 25 },
      { customerId: 5, amount: 4000000, status: 'hoan_thanh', daysAgo: 5 }
    ];

    for (const orderInfo of orderData) {
      const orderDate = new Date();
      orderDate.setDate(orderDate.getDate() - orderInfo.daysAgo);

      const order = await DonHang.create({
        ma_don_hang: `DH${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        khach_hang_id: orderInfo.customerId,
        tong_tien: orderInfo.amount,
        tong_phai_tra: orderInfo.amount,
        con_phai_tra: orderInfo.status === 'hoan_thanh' ? 0 : orderInfo.amount,
        trang_thai: orderInfo.status,
        phuong_thuc_thanh_toan: 'cash',
        ghi_chu: `Test order for debt report`,
        nguoi_tao: 'system',
        ngay_ban: orderDate
      });

      orders.push(order);
    }

    console.log(`✅ Created ${orders.length} orders`);

    // 3. Tạo công nợ cho khách hàng
    const debtData = [
      { customerId: 1, totalDebt: 15000000 },
      { customerId: 2, totalDebt: 12000000 },
      { customerId: 3, totalDebt: 10000000 },
      { customerId: 4, totalDebt: 8000000 },
      { customerId: 5, totalDebt: 7000000 }
    ];

    for (const debt of debtData) {
      await CongNoNguoiDung.upsert({
        nguoi_dung_id: debt.customerId,
        tong_cong_no: debt.totalDebt,
        ghi_chu: 'Công nợ từ đơn hàng test'
      });
    }

    console.log(`✅ Created debt records for ${debtData.length} customers`);

    // 4. Tạo thanh toán test
    const payments = [];
    const paymentData = [
      { customerId: 1, amount: 5000000, daysAgo: 20 },
      { customerId: 2, amount: 3000000, daysAgo: 15 },
      { customerId: 3, amount: 2000000, daysAgo: 10 },
      { customerId: 4, amount: 1000000, daysAgo: 8 },
      { customerId: 5, amount: 4000000, daysAgo: 3 }
    ];

    for (const payment of paymentData) {
      const paymentDate = new Date();
      paymentDate.setDate(paymentDate.getDate() - payment.daysAgo);

      const paymentRecord = await Payment.create({
        customer_id: payment.customerId,
        type: 'thu',
        amount: payment.amount,
        payment_method: 'cash',
        note: 'Test payment for debt report',
        status: 'completed',
        created_at: paymentDate,
        updated_at: paymentDate
      });

      payments.push(paymentRecord);
    }

    console.log(`✅ Created ${payments.length} payments`);

    // 5. Tạo dữ liệu cho các tháng trước
    const monthlyData = [
      { month: -5, orders: 3, totalAmount: 25000000, payments: 15000000 },
      { month: -4, orders: 4, totalAmount: 30000000, payments: 20000000 },
      { month: -3, orders: 2, totalAmount: 20000000, payments: 12000000 },
      { month: -2, orders: 5, totalAmount: 35000000, payments: 25000000 },
      { month: -1, orders: 3, totalAmount: 28000000, payments: 18000000 }
    ];

    for (const monthData of monthlyData) {
      const monthDate = new Date();
      monthDate.setMonth(monthDate.getMonth() + monthData.month);
      monthDate.setDate(15); // Giữa tháng

      // Tạo đơn hàng cho tháng
      for (let i = 0; i < monthData.orders; i++) {
        const orderDate = new Date(monthDate);
        orderDate.setDate(orderDate.getDate() + i * 3);

        await DonHang.create({
          ma_don_hang: `DH${monthDate.getMonth() + 1}-${i + 1}-${Date.now()}`,
          khach_hang_id: (i % 5) + 1,
          tong_tien: monthData.totalAmount / monthData.orders,
          tong_phai_tra: monthData.totalAmount / monthData.orders,
          con_phai_tra: 0,
          trang_thai: 'hoan_thanh',
          phuong_thuc_thanh_toan: 'cash',
          ghi_chu: `Monthly test order`,
          nguoi_tao: 'system',
          ngay_ban: orderDate
        });
      }

      // Tạo thanh toán cho tháng
      const paymentDate = new Date(monthDate);
      paymentDate.setDate(paymentDate.getDate() + 10);

      await Payment.create({
        customer_id: ((monthData.month * -1) % 5) + 1,
        type: 'thu',
        amount: monthData.payments,
        payment_method: 'transfer',
        note: `Monthly payment - Month ${monthDate.getMonth() + 1}`,
        status: 'completed',
        created_at: paymentDate,
        updated_at: paymentDate
      });
    }

    console.log('✅ Created monthly historical data');

    console.log('🎉 Debt report data seeding completed successfully!');
    console.log('📊 Summary:');
    console.log(`   - Customers: 5`);
    console.log(`   - Orders: ${orders.length + monthlyData.reduce((sum, m) => sum + m.orders, 0)}`);
    console.log(`   - Debt records: ${debtData.length}`);
    console.log(`   - Payments: ${payments.length + monthlyData.length}`);

  } catch (error) {
    console.error('❌ Error seeding debt report data:', error);
    throw error;
  }
}

// Chạy script nếu được gọi trực tiếp
if (require.main === module) {
  seedDebtReportData()
    .then(() => {
      console.log('✅ Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedDebtReportData };
