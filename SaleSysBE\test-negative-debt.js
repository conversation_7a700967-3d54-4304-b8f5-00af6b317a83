require('dotenv').config();
const { Ng<PERSON><PERSON><PERSON><PERSON>, CongNoNguoiDung, Payment, sequelize } = require('./models');

async function testNegativeDebt() {
  try {
    console.log('🧪 Testing Negative Debt Feature...\n');

    // 1. Tạo khách hàng test
    console.log('1️⃣ Creating test customer...');
    const customer = await NguoiDung.create({
      ho_ten: 'Nguyễn Văn Test Âm',
      so_dien_thoai: '0987654321',
      email: '<EMAIL>',
      loai_nguoi_dung: 'khach_hang',
      trang_thai: 'dang_giao_dich'
    });
    console.log(`✅ Created customer: ${customer.ho_ten} (ID: ${customer.id})`);

    // 2. Tạo công nợ ban đầu
    console.log('\n2️⃣ Creating initial debt...');
    const debtRecord = await CongNoNguoiDung.create({
      nguoi_dung_id: customer.id,
      tong_cong_no: 1000000 // 1 triệu VND nợ
    });
    console.log(`✅ Initial debt: ${new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(debtRecord.tong_cong_no)}`);

    // 3. Test thu tiền bình thường (không vượt quá nợ)
    console.log('\n3️⃣ Testing normal payment (within debt limit)...');
    await Payment.create({
      customer_id: customer.id,
      type: 'thu',
      amount: 500000, // Thu 500k
      payment_method: 'cash',
      note: 'Thu tiền bình thường',
      created_by: 'test',
      status: 'completed'
    });
    
    // Cập nhật công nợ
    await debtRecord.update({
      tong_cong_no: debtRecord.tong_cong_no - 500000
    });
    await debtRecord.reload();
    
    console.log(`✅ After normal payment: ${new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(debtRecord.tong_cong_no)}`);

    // 4. Test thu tiền vượt quá nợ (tạo công nợ âm)
    console.log('\n4️⃣ Testing overpayment (creating negative debt)...');
    await Payment.create({
      customer_id: customer.id,
      type: 'thu',
      amount: 800000, // Thu 800k nhưng chỉ còn nợ 500k
      payment_method: 'transfer',
      note: 'Thu tiền vượt quá nợ - tạo số dư thừa',
      created_by: 'test',
      status: 'completed'
    });
    
    // Cập nhật công nợ (cho phép âm)
    await debtRecord.update({
      tong_cong_no: debtRecord.tong_cong_no - 800000
    });
    await debtRecord.reload();
    
    console.log(`✅ After overpayment: ${new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(debtRecord.tong_cong_no)} (NEGATIVE - Customer has surplus)`);

    // 5. Test hoàn tiền cho khách hàng thừa tiền
    console.log('\n5️⃣ Testing refund to customer with surplus...');
    await Payment.create({
      customer_id: customer.id,
      type: 'chi',
      amount: 200000, // Hoàn 200k
      payment_method: 'cash',
      note: 'Hoàn tiền cho khách hàng thừa',
      created_by: 'test',
      status: 'completed'
    });
    
    // Cập nhật công nợ (chi tiền = tăng công nợ, nhưng vẫn âm)
    await debtRecord.update({
      tong_cong_no: debtRecord.tong_cong_no + 200000
    });
    await debtRecord.reload();
    
    console.log(`✅ After refund: ${new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(debtRecord.tong_cong_no)} (Still negative but reduced)`);

    // 6. Kiểm tra lịch sử thanh toán
    console.log('\n6️⃣ Checking payment history...');
    const payments = await Payment.findAll({
      where: { customer_id: customer.id },
      order: [['created_at', 'ASC']]
    });
    
    console.log('📋 Payment History:');
    payments.forEach((payment, index) => {
      const typeText = payment.type === 'thu' ? 'Thu tiền' : 'Hoàn tiền';
      const amount = new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(payment.amount);
      
      console.log(`   ${index + 1}. ${typeText}: ${amount} (${payment.payment_method}) - ${payment.note}`);
    });

    // 7. Test các trường hợp edge case
    console.log('\n7️⃣ Testing edge cases...');
    
    // Trường hợp thu tiền khi đã âm (tăng thêm số dư thừa)
    await Payment.create({
      customer_id: customer.id,
      type: 'thu',
      amount: 100000,
      payment_method: 'card',
      note: 'Thu thêm tiền khi đã thừa',
      created_by: 'test',
      status: 'completed'
    });
    
    await debtRecord.update({
      tong_cong_no: debtRecord.tong_cong_no - 100000
    });
    await debtRecord.reload();
    
    console.log(`✅ After additional payment when already negative: ${new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(debtRecord.tong_cong_no)}`);

    // 8. Tổng kết
    console.log('\n📊 SUMMARY:');
    console.log(`👤 Customer: ${customer.ho_ten}`);
    console.log(`💰 Final debt/surplus: ${new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(debtRecord.tong_cong_no)}`);
    
    if (debtRecord.tong_cong_no < 0) {
      console.log(`🎉 SUCCESS: Customer has surplus of ${new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(Math.abs(debtRecord.tong_cong_no))}`);
    } else if (debtRecord.tong_cong_no === 0) {
      console.log('✅ SUCCESS: Customer has no debt');
    } else {
      console.log(`⚠️  Customer still owes ${new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(debtRecord.tong_cong_no)}`);
    }
    
    console.log(`📝 Total payments: ${payments.length}`);
    console.log('\n🎯 Negative debt feature is working correctly!');

  } catch (error) {
    console.error('❌ Error testing negative debt:', error);
  } finally {
    await sequelize.close();
  }
}

// Chạy test
testNegativeDebt();
