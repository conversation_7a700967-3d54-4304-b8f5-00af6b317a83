require('dotenv').config();
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, sequelize } = require('./models');

async function testActualDebtNoStatus() {
  try {
    console.log('🧪 Testing Actual Debt - No Status Dependency...\n');

    // 1. Tạo khách hàng test
    console.log('1️⃣ Creating test customer...');
    const customer = await NguoiDung.create({
      ho_ten: 'Nguyễn Văn Test No Status',
      so_dien_thoai: '0987654444',
      email: '<EMAIL>',
      loai_nguoi_dung: 'khach_hang',
      trang_thai: 'dang_giao_dich'
    });
    console.log(`✅ Created customer: ${customer.ho_ten} (ID: ${customer.id})`);

    // 2. Tạo đơn hàng với các trạng thái khác nhau
    console.log('\n2️⃣ Creating orders with different statuses...');
    
    const orders = [
      {
        ma_don_hang: `NO-STATUS-1-${Date.now()}`,
        trang_thai: 'draft', // Nháp
        tong_tien: 1000000,
        tong_da_tra: 0,
        tien_coc: 200000,
        expected_debt: 800000,
        desc: 'Draft order'
      },
      {
        ma_don_hang: `NO-STATUS-2-${Date.now()}`,
        trang_thai: 'da_xac_nhan', // Đã xác nhận
        tong_tien: 800000,
        tong_da_tra: 300000,
        tien_coc: 100000,
        expected_debt: 400000,
        desc: 'Confirmed order'
      },
      {
        ma_don_hang: `NO-STATUS-3-${Date.now()}`,
        trang_thai: 'da_dong_goi', // Đã đóng gói
        tong_tien: 600000,
        tong_da_tra: 200000,
        tien_coc: 150000,
        expected_debt: 250000,
        desc: 'Packed order'
      },
      {
        ma_don_hang: `NO-STATUS-4-${Date.now()}`,
        trang_thai: 'da_giao', // Đã giao
        tong_tien: 500000,
        tong_da_tra: 100000,
        tien_coc: 50000,
        expected_debt: 350000,
        desc: 'Delivered order'
      },
      {
        ma_don_hang: `NO-STATUS-5-${Date.now()}`,
        trang_thai: 'hoan_thanh', // Hoàn thành
        tong_tien: 900000,
        tong_da_tra: 400000,
        tien_coc: 300000,
        expected_debt: 200000,
        desc: 'Completed order'
      },
      {
        ma_don_hang: `NO-STATUS-6-${Date.now()}`,
        trang_thai: 'hoan_hang', // Hoàn hàng
        tong_tien: 700000,
        tong_da_tra: 0,
        tien_coc: 100000,
        expected_debt: 600000, // Vẫn tính theo công thức
        desc: 'Returned order'
      },
      {
        ma_don_hang: `NO-STATUS-7-${Date.now()}`,
        trang_thai: 'huy', // Hủy
        tong_tien: 400000,
        tong_da_tra: 50000,
        tien_coc: 100000,
        expected_debt: 250000, // Vẫn tính theo công thức
        desc: 'Cancelled order'
      }
    ];

    const createdOrders = [];
    for (const orderData of orders) {
      const order = await DonHang.create({
        ma_don_hang: orderData.ma_don_hang,
        khach_hang_id: customer.id,
        ngay_ban: new Date(),
        trang_thai: orderData.trang_thai,
        tong_tien: orderData.tong_tien,
        tong_phai_tra: orderData.tong_tien * 0.5, // Giả sử COD = 50% tổng tiền
        tong_da_tra: orderData.tong_da_tra,
        con_phai_tra: orderData.tong_tien - orderData.tong_da_tra - orderData.tien_coc,
        tien_cod: orderData.tong_tien * 0.5,
        tien_coc: orderData.tien_coc,
        ghi_chu: `Test ${orderData.desc}`
      });
      
      createdOrders.push({
        ...order.dataValues,
        expected_debt: orderData.expected_debt,
        desc: orderData.desc
      });
      
      console.log(`   ✅ ${orderData.desc}: ${orderData.ma_don_hang}`);
    }

    // 3. Test công thức mới (không phụ thuộc trạng thái)
    console.log('\n3️⃣ Testing new formula (status-independent)...');
    
    let totalExpectedDebt = 0;
    let totalCalculatedDebt = 0;
    
    console.log('📊 Testing each order:');
    
    createdOrders.forEach((order, index) => {
      const productAmount = parseFloat(order.tong_tien) || 0;
      const paidAmount = parseFloat(order.tong_da_tra) || 0;
      const depositAmount = parseFloat(order.tien_coc) || 0;
      const calculatedDebt = Math.max(0, productAmount - paidAmount - depositAmount);
      
      totalExpectedDebt += order.expected_debt;
      totalCalculatedDebt += calculatedDebt;
      
      const isCorrect = calculatedDebt === order.expected_debt;
      const status = isCorrect ? '✅' : '❌';
      
      console.log(`\n   ${status} Order ${index + 1}: ${order.desc}`);
      console.log(`     - Status: ${order.trang_thai}`);
      console.log(`     - Formula: ${productAmount.toLocaleString('vi-VN')} - ${paidAmount.toLocaleString('vi-VN')} - ${depositAmount.toLocaleString('vi-VN')}`);
      console.log(`     - Calculated: ${calculatedDebt.toLocaleString('vi-VN')}đ`);
      console.log(`     - Expected: ${order.expected_debt.toLocaleString('vi-VN')}đ`);
      console.log(`     - Match: ${isCorrect}`);
    });

    console.log(`\n📈 Total Summary:`);
    console.log(`   - Total Expected: ${totalExpectedDebt.toLocaleString('vi-VN')}đ`);
    console.log(`   - Total Calculated: ${totalCalculatedDebt.toLocaleString('vi-VN')}đ`);
    console.log(`   - Match: ${totalCalculatedDebt === totalExpectedDebt ? '✅' : '❌'}`);

    // 4. Test API simulation
    console.log('\n4️⃣ Simulating API logic...');
    
    const customerOrders = await DonHang.findAll({
      where: { khach_hang_id: customer.id }
    });

    // Simulate new actualDebt calculation (no status dependency)
    const apiActualDebt = customerOrders.reduce((sum, order) => {
      const productAmount = parseFloat(order.tong_tien) || 0;
      const paidAmount = parseFloat(order.tong_da_tra) || 0;
      const depositAmount = parseFloat(order.tien_coc) || 0;
      const orderActualDebt = productAmount - paidAmount - depositAmount;
      return sum + Math.max(0, orderActualDebt);
    }, 0);

    console.log(`📡 API Calculated Actual Debt: ${apiActualDebt.toLocaleString('vi-VN')}đ`);

    // 5. Test edge cases
    console.log('\n5️⃣ Testing edge cases...');
    
    // Edge case 1: Thanh toán thừa
    const edgeOrder1 = await DonHang.create({
      ma_don_hang: `EDGE-1-${Date.now()}`,
      khach_hang_id: customer.id,
      ngay_ban: new Date(),
      trang_thai: 'hoan_thanh',
      tong_tien: 500000,
      tong_da_tra: 600000, // Trả thừa
      tien_coc: 100000,
      ghi_chu: 'Edge case: Overpaid'
    });

    const edge1Debt = Math.max(0, 500000 - 600000 - 100000); // Should be 0
    console.log(`   Edge Case 1 (Overpaid): ${edge1Debt.toLocaleString('vi-VN')}đ (should be 0)`);

    // Edge case 2: Không có tiền cọc
    const edgeOrder2 = await DonHang.create({
      ma_don_hang: `EDGE-2-${Date.now()}`,
      khach_hang_id: customer.id,
      ngay_ban: new Date(),
      trang_thai: 'da_xac_nhan',
      tong_tien: 300000,
      tong_da_tra: 100000,
      tien_coc: 0, // Không có cọc
      ghi_chu: 'Edge case: No deposit'
    });

    const edge2Debt = Math.max(0, 300000 - 100000 - 0); // Should be 200000
    console.log(`   Edge Case 2 (No deposit): ${edge2Debt.toLocaleString('vi-VN')}đ (should be 200,000)`);

    // 6. Cleanup
    console.log('\n6️⃣ Cleaning up...');
    await DonHang.destroy({ where: { khach_hang_id: customer.id } });
    await customer.destroy();
    console.log('✅ Cleanup completed');

    // 7. Final verification
    console.log('\n7️⃣ Final Verification:');
    
    if (apiActualDebt === totalCalculatedDebt && edge1Debt === 0 && edge2Debt === 200000) {
      console.log('🎉 All tests passed! Status-independent actual debt calculation works correctly.');
      console.log('✅ Formula: MAX(0, tong_tien - tong_da_tra - tien_coc)');
      console.log('✅ No status dependency');
      console.log('✅ Handles all order statuses equally');
      console.log('✅ Prevents negative debt');
      console.log('✅ Handles edge cases correctly');
    } else {
      console.log('❌ Tests failed!');
      console.log(`   API Result: ${apiActualDebt.toLocaleString('vi-VN')}đ`);
      console.log(`   Expected: ${totalCalculatedDebt.toLocaleString('vi-VN')}đ`);
      console.log(`   Edge 1: ${edge1Debt} (expected: 0)`);
      console.log(`   Edge 2: ${edge2Debt} (expected: 200000)`);
    }

    // 8. Show benefits
    console.log('\n8️⃣ Benefits of Status-Independent Calculation:');
    console.log('📋 Advantages:');
    console.log('   ✅ Simple and consistent formula');
    console.log('   ✅ No complex status logic');
    console.log('   ✅ Works for all order types');
    console.log('   ✅ Easy to understand and debug');
    console.log('   ✅ Reflects actual financial obligation');
    console.log('   ✅ Status changes don\'t affect debt calculation');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await sequelize.close();
  }
}

// Chạy test
testActualDebtNoStatus();
