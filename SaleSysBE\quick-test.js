// Quick test for debt report API
const { getDebtReport } = require('./controllers/debtController');

// Mock request and response objects
const mockReq = {
  query: {},
  user: { username: 'test' }
};

const mockRes = {
  json: (data) => {
    console.log('✅ API Response:', JSON.stringify(data, null, 2));
  },
  status: (code) => ({
    json: (data) => {
      console.log(`❌ Error ${code}:`, JSON.stringify(data, null, 2));
    }
  })
};

console.log('🧪 Testing getDebtReport function directly...');

getDebtReport(mockReq, mockRes)
  .then(() => {
    console.log('✅ Test completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  });
