const {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  PhienBan<PERSON>an<PERSON>ham,
  SanPham,
  Payment,
  NhomKhachHang,
  NhomKhachHangNguoiDung,
  sequelize,
} = require("../models");
const { Op } = require("sequelize");
const dayjs = require("dayjs");

// Helper function để xử lý array parameters từ query string
const parseArrayParam = (param) => {
  if (!param) return [];
  if (Array.isArray(param)) return param;
  if (typeof param === "string") {
    // Nếu là string có dấu phẩy, split nó
    if (param.includes(",")) return param.split(",").map((item) => item.trim());
    // Nếu là string đơn, wrap trong array
    return [param];
  }
  return [];
};

// Báo cáo tổng quan
const getOverviewReport = async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      period = "day",
      // <PERSON><PERSON> lọ<PERSON> cơ bản cho overview
      customerGroups,
      customers,
      orderStatus,
      minOrderValue,
      maxOrderValue,
    } = req.query;

    console.log("📊 Getting overview report...", {
      startDate,
      endDate,
      period,
      filters: {
        customerGroups,
        customers,
        orderStatus,
        minOrderValue,
        maxOrderValue,
      },
    });

    // Parse array parameters
    const parsedCustomerGroups = parseArrayParam(customerGroups);
    const parsedCustomers = parseArrayParam(customers);
    const parsedOrderStatus = parseArrayParam(orderStatus);

    // Set timeout cho response
    req.setTimeout(30000); // 30 seconds

    const start = startDate
      ? dayjs(startDate).startOf("day")
      : dayjs().subtract(30, "days").startOf("day");
    const end = endDate ? dayjs(endDate).endOf("day") : dayjs().endOf("day");

    // Giới hạn tối đa 90 ngày để tránh timeout
    const daysDiff = end.diff(start, "days");
    if (daysDiff > 90) {
      return res.status(400).json({
        success: false,
        message: "Khoảng thời gian không được vượt quá 90 ngày",
      });
    }

    console.log(
      `📅 Date range: ${start.format("YYYY-MM-DD")} to ${end.format(
        "YYYY-MM-DD"
      )} (${daysDiff} days)`
    );

    // Tổng quan chung
    console.log("🔍 Fetching overview data...");
    const overview = await Promise.all([
      // Tổng doanh thu
      DonHang.sum("tong_phai_tra", {
        where: {
          ngay_ban: { [Op.between]: [start.toDate(), end.toDate()] },
          trang_thai: {
            [Op.in]: ["da_xac_nhan", "da_dong_goi", "da_giao", "hoan_thanh"],
          },
        },
      }),
      // Tổng đơn hàng
      DonHang.count({
        where: {
          ngay_ban: { [Op.between]: [start.toDate(), end.toDate()] },
        },
      }),
      // Tổng khách hàng - sử dụng raw query
      sequelize.query(
        `
        SELECT COUNT(DISTINCT khach_hang_id) as total
        FROM don_hang
        WHERE ngay_ban BETWEEN :startDate AND :endDate
        AND khach_hang_id IS NOT NULL
      `,
        {
          replacements: { startDate: start.toDate(), endDate: end.toDate() },
          type: sequelize.QueryTypes.SELECT,
        }
      ),
      // Tổng sản phẩm bán
      sequelize.query(
        `
        SELECT COALESCE(SUM(dhsp.so_luong), 0) as total
        FROM don_hang_san_pham dhsp
        INNER JOIN don_hang dh ON dhsp.don_hang_id = dh.id
        WHERE dh.ngay_ban BETWEEN :startDate AND :endDate
        AND dh.trang_thai IN ('da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh')
      `,
        {
          replacements: { startDate: start.toDate(), endDate: end.toDate() },
          type: sequelize.QueryTypes.SELECT,
        }
      ),
    ]);

    const [
      totalRevenue,
      totalOrders,
      totalCustomersResult,
      totalProductsResult,
    ] = overview;
    const totalCustomers = totalCustomersResult[0]?.total || 0;
    const totalProducts = totalProductsResult[0]?.total || 0;

    console.log("📈 Overview results:", {
      totalRevenue,
      totalOrders,
      totalCustomers,
      totalProducts,
    });

    // Dữ liệu theo thời gian - sử dụng raw query để tối ưu
    console.log("📊 Fetching time data...");
    const timeDataResult = await sequelize.query(
      `
      SELECT
        DATE(ngay_ban) as date,
        COALESCE(SUM(CASE WHEN trang_thai IN ('da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh') THEN tong_phai_tra ELSE 0 END), 0) as revenue,
        COUNT(*) as orders
      FROM don_hang
      WHERE ngay_ban BETWEEN :startDate AND :endDate
      GROUP BY DATE(ngay_ban)
      ORDER BY DATE(ngay_ban)
    `,
      {
        replacements: { startDate: start.toDate(), endDate: end.toDate() },
        type: sequelize.QueryTypes.SELECT,
      }
    );

    // Tạo timeData đơn giản từ kết quả query
    const timeData = timeDataResult.map((item) => ({
      date: dayjs(item.date).format("YYYY-MM-DD"),
      dateDisplay: dayjs(item.date).format("DD/MM"),
      revenue: parseFloat(item.revenue) || 0,
      orders: parseInt(item.orders) || 0,
    }));

    console.log(`📊 Time data processed: ${timeData.length} days`);

    console.log(
      `✅ Overview report completed. TimeData length: ${timeData.length}`
    );

    res.json({
      success: true,
      data: {
        overview: {
          totalRevenue: totalRevenue || 0,
          totalOrders: totalOrders || 0,
          totalCustomers: totalCustomers || 0,
          totalProducts: totalProducts || 0,
          averageOrderValue:
            totalOrders > 0 ? (totalRevenue || 0) / totalOrders : 0,
        },
        timeData,
        period: {
          startDate: start.format("YYYY-MM-DD"),
          endDate: end.format("YYYY-MM-DD"),
          days: end.diff(start, "days") + 1,
        },
      },
    });
  } catch (error) {
    console.error("❌ Error in getOverviewReport:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy báo cáo tổng quan",
      error: error.message,
    });
  }
};

// Báo cáo sản phẩm
const getProductReport = async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      limit = 20,
      // Bộ lọc nâng cao
      customerGroups,
      customers,
      productCategories,
      orderStatus,
      minOrderValue,
      maxOrderValue,
    } = req.query;

    console.log("📦 Getting product report...", {
      startDate,
      endDate,
      limit,
      filters: {
        customerGroups,
        customers,
        productCategories,
        orderStatus,
        minOrderValue,
        maxOrderValue,
      },
    });

    // Parse array parameters
    const parsedCustomerGroups = parseArrayParam(customerGroups);
    const parsedCustomers = parseArrayParam(customers);
    const parsedProductCategories = parseArrayParam(productCategories);
    const parsedOrderStatus = parseArrayParam(orderStatus);

    const start = startDate
      ? dayjs(startDate).startOf("day")
      : dayjs().subtract(30, "days").startOf("day");
    const end = endDate ? dayjs(endDate).endOf("day") : dayjs().endOf("day");

    // Xây dựng điều kiện WHERE động cho sản phẩm
    let whereConditions = [];
    let replacements = {
      startDate: start.toDate(),
      endDate: end.toDate(),
      limit: parseInt(limit),
    };

    // Bộ lọc nhóm khách hàng
    if (parsedCustomerGroups.length > 0) {
      whereConditions.push(
        `nkh.id IN (${parsedCustomerGroups
          .map((_, i) => `:customerGroup${i}`)
          .join(", ")})`
      );
      parsedCustomerGroups.forEach((id, i) => {
        replacements[`customerGroup${i}`] = parseInt(id);
      });
    }

    // Bộ lọc khách hàng cụ thể
    if (parsedCustomers.length > 0) {
      whereConditions.push(
        `dh.khach_hang_id IN (${parsedCustomers
          .map((_, i) => `:customer${i}`)
          .join(", ")})`
      );
      parsedCustomers.forEach((id, i) => {
        replacements[`customer${i}`] = parseInt(id);
      });
    }

    // Bộ lọc danh mục sản phẩm
    if (parsedProductCategories.length > 0) {
      whereConditions.push(
        `sp.loai_san_pham_id IN (${parsedProductCategories
          .map((_, i) => `:category${i}`)
          .join(", ")})`
      );
      parsedProductCategories.forEach((id, i) => {
        replacements[`category${i}`] = parseInt(id);
      });
    }

    // Bộ lọc trạng thái đơn hàng
    if (parsedOrderStatus.length > 0) {
      whereConditions.push(
        `dh.trang_thai IN (${parsedOrderStatus
          .map((_, i) => `:orderStatus${i}`)
          .join(", ")})`
      );
      parsedOrderStatus.forEach((status, i) => {
        replacements[`orderStatus${i}`] = status;
      });
    }

    // Bộ lọc giá trị đơn hàng
    if (minOrderValue) {
      whereConditions.push("dh.tong_phai_tra >= :minOrderValue");
      replacements.minOrderValue = parseFloat(minOrderValue);
    }
    if (maxOrderValue) {
      whereConditions.push("dh.tong_phai_tra <= :maxOrderValue");
      replacements.maxOrderValue = parseFloat(maxOrderValue);
    }

    // Tạo chuỗi WHERE động
    const dynamicWhere =
      whereConditions.length > 0 ? `AND ${whereConditions.join(" AND ")}` : "";

    // Top sản phẩm bán chạy với chi tiết đầy đủ và bộ lọc
    const topProducts = await sequelize.query(
      `
      SELECT
        dhsp.phien_ban_san_pham_id,
        dhsp.ten_san_pham as name,
        pbsp.ma as sku,

        -- Số lượng bán
        SUM(dhsp.so_luong) as total_quantity,

        -- Số đơn hàng
        COUNT(DISTINCT dhsp.don_hang_id) as orders,

        -- Tiền hàng (tổng tiền trước chiết khấu)
        SUM(dhsp.so_luong * dhsp.don_gia) as total_amount,

        -- Tiền hàng trả lại
        SUM(CASE WHEN dh.trang_thai = 'hoan_hang' THEN dhsp.so_luong * dhsp.don_gia ELSE 0 END) as returned_amount,

        -- Doanh thu (không tính hàng hoàn trả)
        SUM(CASE WHEN dh.trang_thai != 'hoan_hang' THEN dhsp.thanh_tien ELSE 0 END) as revenue,

        -- Chi phí ước tính
        SUM(dhsp.so_luong * COALESCE(pbsp.gia_nhap, 0)) as total_cost,

        -- Lợi nhuận gộp
        SUM(CASE WHEN dh.trang_thai != 'hoan_hang' THEN dhsp.thanh_tien ELSE 0 END) -
        SUM(dhsp.so_luong * COALESCE(pbsp.gia_nhap, 0)) as gross_profit,

        AVG(dhsp.don_gia) as avg_price

      FROM don_hang_san_pham dhsp
      INNER JOIN don_hang dh ON dhsp.don_hang_id = dh.id
      LEFT JOIN phien_ban_san_pham pbsp ON dhsp.phien_ban_san_pham_id = pbsp.id
      LEFT JOIN san_pham sp ON pbsp.san_pham_id = sp.id
      LEFT JOIN nhom_khach_hang_nguoi_dung nkhnd ON dh.khach_hang_id = nkhnd.nguoi_dung_id
      LEFT JOIN nhom_khach_hang nkh ON nkhnd.nhom_khach_hang_id = nkh.id
      WHERE dh.ngay_ban BETWEEN :startDate AND :endDate
      AND dh.trang_thai IN ('da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh', 'hoan_hang')
      ${dynamicWhere}
      GROUP BY dhsp.phien_ban_san_pham_id, dhsp.ten_san_pham, pbsp.ma
      ORDER BY revenue DESC
      LIMIT :limit
    `,
      {
        replacements,
        type: sequelize.QueryTypes.SELECT,
      }
    );

    // Sản phẩm theo danh mục (giả lập)
    const categoryData = topProducts.slice(0, 5).map((product, index) => ({
      category: `Danh mục ${index + 1}`,
      quantity: parseInt(product.total_quantity),
      revenue: parseFloat(product.revenue),
      color: ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"][index],
    }));

    res.json({
      success: true,
      data: {
        topProducts: topProducts.map((product) => ({
          id: product.phien_ban_san_pham_id,
          name: product.name,
          sku: product.sku || `SKU${product.phien_ban_san_pham_id}`,
          quantity: parseInt(product.total_quantity),
          orders: parseInt(product.orders),
          totalAmount: parseFloat(product.total_amount),
          returnedAmount: parseFloat(product.returned_amount),
          revenue: parseFloat(product.revenue),
          totalCost: parseFloat(product.total_cost),
          grossProfit: parseFloat(product.gross_profit),
          avgPrice: parseFloat(product.avg_price),
        })),
        categoryData,
        period: {
          startDate: start.format("YYYY-MM-DD"),
          endDate: end.format("YYYY-MM-DD"),
        },
      },
    });
  } catch (error) {
    console.error("❌ Error in getProductReport:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy báo cáo sản phẩm",
      error: error.message,
    });
  }
};

// Báo cáo khách hàng
const getCustomerReport = async (req, res) => {
  try {
    const { startDate, endDate, limit = 20 } = req.query;

    console.log("👥 Getting customer report...", { startDate, endDate, limit });

    const start = startDate
      ? dayjs(startDate).startOf("day")
      : dayjs().subtract(30, "days").startOf("day");
    const end = endDate ? dayjs(endDate).endOf("day") : dayjs().endOf("day");

    // Top khách hàng
    const topCustomers = await sequelize.query(
      `
      SELECT 
        dh.khach_hang_id,
        nd.ho_ten,
        nd.so_dien_thoai,
        COUNT(dh.id) as total_orders,
        SUM(dh.tong_phai_tra) as total_spent,
        AVG(dh.tong_phai_tra) as avg_order_value,
        MAX(dh.ngay_ban) as last_order_date
      FROM don_hang dh
      INNER JOIN nguoi_dung nd ON dh.khach_hang_id = nd.id
      WHERE dh.ngay_ban BETWEEN :startDate AND :endDate
      AND dh.trang_thai IN ('da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh')
      AND dh.khach_hang_id IS NOT NULL
      GROUP BY dh.khach_hang_id, nd.ho_ten, nd.so_dien_thoai
      ORDER BY total_spent DESC
      LIMIT :limit
    `,
      {
        replacements: {
          startDate: start.toDate(),
          endDate: end.toDate(),
          limit: parseInt(limit),
        },
        type: sequelize.QueryTypes.SELECT,
      }
    );

    // Phân tích khách hàng mới vs cũ
    const customerAnalysis = await Promise.all([
      // Khách hàng mới
      NguoiDung.count({
        where: {
          ngay_tao: { [Op.between]: [start.toDate(), end.toDate()] },
          loai_nguoi_dung: "khach_hang",
        },
      }),
      // Khách hàng quay lại
      sequelize.query(
        `
        SELECT COUNT(DISTINCT dh.khach_hang_id) as returning_customers
        FROM don_hang dh
        WHERE dh.ngay_ban BETWEEN :startDate AND :endDate
        AND dh.khach_hang_id IN (
          SELECT DISTINCT dh2.khach_hang_id 
          FROM don_hang dh2 
          WHERE dh2.ngay_ban < :startDate
          AND dh2.khach_hang_id IS NOT NULL
        )
      `,
        {
          replacements: { startDate: start.toDate(), endDate: end.toDate() },
          type: sequelize.QueryTypes.SELECT,
        }
      ),
    ]);

    const [newCustomers, returningCustomersResult] = customerAnalysis;
    const returningCustomers =
      returningCustomersResult[0]?.returning_customers || 0;

    res.json({
      success: true,
      data: {
        topCustomers: topCustomers.map((customer) => ({
          id: customer.khach_hang_id,
          name: customer.ho_ten,
          phone: customer.so_dien_thoai,
          totalOrders: parseInt(customer.total_orders),
          totalSpent: parseFloat(customer.total_spent),
          avgOrderValue: parseFloat(customer.avg_order_value),
          lastOrderDate: customer.last_order_date,
        })),
        customerAnalysis: {
          newCustomers: newCustomers || 0,
          returningCustomers: returningCustomers || 0,
          totalActiveCustomers: (newCustomers || 0) + (returningCustomers || 0),
        },
        period: {
          startDate: start.format("YYYY-MM-DD"),
          endDate: end.format("YYYY-MM-DD"),
        },
      },
    });
  } catch (error) {
    console.error("❌ Error in getCustomerReport:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy báo cáo khách hàng",
      error: error.message,
    });
  }
};

// Báo cáo hoạt động kinh doanh theo khách hàng
const getBusinessActivityReport = async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      customerType = "all",
      limit = 50,
      // Bộ lọc nâng cao
      customerGroups,
      customers,
      customerTypes,
      productCategories,
      orderStatus,
      paymentMethods,
      salesChannels,
      minOrderValue,
      maxOrderValue,
      minQuantity,
      maxQuantity,
    } = req.query;

    console.log("📈 Getting business activity report...", {
      startDate,
      endDate,
      customerType,
      limit,
      filters: {
        customerGroups,
        customers,
        customerTypes,
        productCategories,
        orderStatus,
        paymentMethods,
        salesChannels,
        minOrderValue,
        maxOrderValue,
        minQuantity,
        maxQuantity,
      },
    });

    console.log("🔍 Raw query params:", req.query);

    // Helper function để xử lý array parameters
    const parseArrayParam = (param) => {
      if (!param) return [];
      if (Array.isArray(param)) return param;
      if (typeof param === "string") {
        // Nếu là string có dấu phẩy, split nó
        if (param.includes(","))
          return param.split(",").map((item) => item.trim());
        // Nếu là string đơn, wrap trong array
        return [param];
      }
      return [];
    };

    // Parse array parameters
    const parsedCustomerGroups = parseArrayParam(customerGroups);
    const parsedCustomers = parseArrayParam(customers);
    const parsedCustomerTypes = parseArrayParam(customerTypes);
    const parsedProductCategories = parseArrayParam(productCategories);
    const parsedOrderStatus = parseArrayParam(orderStatus);
    const parsedPaymentMethods = parseArrayParam(paymentMethods);
    const parsedSalesChannels = parseArrayParam(salesChannels);

    console.log("🔍 Parsed filter params:", {
      parsedCustomerGroups,
      parsedCustomers,
      parsedCustomerTypes,
      parsedProductCategories,
      parsedOrderStatus,
      parsedPaymentMethods,
      parsedSalesChannels,
    });

    const start = startDate
      ? dayjs(startDate).startOf("day")
      : dayjs().subtract(30, "days").startOf("day");
    const end = endDate ? dayjs(endDate).endOf("day") : dayjs().endOf("day");

    // Xây dựng điều kiện WHERE động dựa trên bộ lọc
    let whereConditions = [];
    let replacements = {
      startDate: start.toDate(),
      endDate: end.toDate(),
      limit: parseInt(limit),
    };

    // Bộ lọc nhóm khách hàng
    if (parsedCustomerGroups.length > 0) {
      whereConditions.push(
        `nkh.id IN (${parsedCustomerGroups
          .map((_, i) => `:customerGroup${i}`)
          .join(", ")})`
      );
      parsedCustomerGroups.forEach((id, i) => {
        replacements[`customerGroup${i}`] = parseInt(id);
      });
    }

    // Bộ lọc khách hàng cụ thể
    if (parsedCustomers.length > 0) {
      whereConditions.push(
        `nd.id IN (${parsedCustomers
          .map((_, i) => `:customer${i}`)
          .join(", ")})`
      );
      parsedCustomers.forEach((id, i) => {
        replacements[`customer${i}`] = parseInt(id);
      });
    }

    // Bộ lọc trạng thái đơn hàng
    if (parsedOrderStatus.length > 0) {
      whereConditions.push(
        `dh.trang_thai IN (${parsedOrderStatus
          .map((_, i) => `:orderStatus${i}`)
          .join(", ")})`
      );
      parsedOrderStatus.forEach((status, i) => {
        replacements[`orderStatus${i}`] = status;
      });
    }

    // Bộ lọc giá trị đơn hàng
    if (minOrderValue) {
      whereConditions.push("dh.tong_phai_tra >= :minOrderValue");
      replacements.minOrderValue = parseFloat(minOrderValue);
    }
    if (maxOrderValue) {
      whereConditions.push("dh.tong_phai_tra <= :maxOrderValue");
      replacements.maxOrderValue = parseFloat(maxOrderValue);
    }

    // Tạo chuỗi WHERE động
    const dynamicWhere =
      whereConditions.length > 0 ? `AND ${whereConditions.join(" AND ")}` : "";

    // Query chi tiết hoạt động kinh doanh theo khách hàng với bộ lọc
    const businessActivity = await sequelize.query(
      `
      SELECT
        nd.id as customer_id,
        nd.ho_ten as customer_name,
        nd.so_dien_thoai as customer_phone,
        nd.email as customer_email,
        nkh.ten_nhom as customer_group,
        nkh.ma_nhom as customer_group_code,

        -- Số lượng hàng bán ra (tổng số lượng sản phẩm)
        COALESCE(SUM(dhsp.so_luong), 0) as quantity_sold,

        -- Số lượng hàng thực bán (không tính hàng hoàn trả)
        COALESCE(SUM(CASE WHEN dh.trang_thai != 'hoan_hang' THEN dhsp.so_luong ELSE 0 END), 0) as actual_quantity_sold,

        -- Số đơn hàng
        COUNT(DISTINCT dh.id) as order_count,

        -- Tiền hàng (tổng tiền trước chiết khấu)
        COALESCE(SUM(dh.tong_tien), 0) as total_amount,

        -- Tiền hàng trả lại
        COALESCE(SUM(CASE WHEN dh.trang_thai = 'hoan_hang' THEN dh.tong_tien ELSE 0 END), 0) as returned_amount,

        -- Tiền chiết khấu
        COALESCE(SUM(dh.chiet_khau), 0) as discount_amount,

        -- Phí giao hàng
        COALESCE(SUM(dh.phi_giao_hang), 0) as shipping_fee,

        -- Doanh thu (tổng phải trả)
        COALESCE(SUM(CASE WHEN dh.trang_thai != 'hoan_hang' THEN dh.tong_phai_tra ELSE 0 END), 0) as revenue,

        -- Tổng chi phí (ước tính từ giá nhập)
        COALESCE(SUM(dhsp.so_luong * pbsp.gia_nhap), 0) as total_cost,

        -- Lợi nhuận gộp
        COALESCE(SUM(CASE WHEN dh.trang_thai != 'hoan_hang' THEN dh.tong_phai_tra ELSE 0 END), 0) -
        COALESCE(SUM(dhsp.so_luong * pbsp.gia_nhap), 0) as gross_profit,

        -- Tổng đã mua (tổng giá trị đơn hàng)
        COALESCE(SUM(dh.tong_phai_tra), 0) as total_purchased,

        -- Ngày cập nhật cuối
        MAX(dh.ngay_ban) as last_updated

      FROM nguoi_dung nd
      LEFT JOIN don_hang dh ON nd.id = dh.khach_hang_id
        AND dh.ngay_ban BETWEEN :startDate AND :endDate
        AND dh.trang_thai IN ('da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh', 'hoan_hang')
      LEFT JOIN don_hang_san_pham dhsp ON dh.id = dhsp.don_hang_id
      LEFT JOIN phien_ban_san_pham pbsp ON dhsp.phien_ban_san_pham_id = pbsp.id
      LEFT JOIN nhom_khach_hang_nguoi_dung nkhnd ON nd.id = nkhnd.nguoi_dung_id
      LEFT JOIN nhom_khach_hang nkh ON nkhnd.nhom_khach_hang_id = nkh.id

      WHERE nd.loai_nguoi_dung = 'khach_hang'
      AND nd.trang_thai = 'dang_giao_dich'
      ${dynamicWhere}

      GROUP BY nd.id, nd.ho_ten, nd.so_dien_thoai, nd.email, nkh.ten_nhom, nkh.ma_nhom
      HAVING COUNT(DISTINCT dh.id) > 0  -- Chỉ lấy khách hàng có đơn hàng
      ORDER BY revenue DESC
      LIMIT :limit
    `,
      {
        replacements,
        type: sequelize.QueryTypes.SELECT,
      }
    );

    // Tính tổng
    const summary = businessActivity.reduce(
      (acc, customer) => {
        acc.totalCustomers += 1;
        acc.totalQuantitySold += parseInt(customer.quantity_sold);
        acc.totalActualQuantitySold += parseInt(customer.actual_quantity_sold);
        acc.totalOrderCount += parseInt(customer.order_count);
        acc.totalAmount += parseFloat(customer.total_amount);
        acc.totalReturnedAmount += parseFloat(customer.returned_amount);
        acc.totalDiscountAmount += parseFloat(customer.discount_amount);
        acc.totalShippingFee += parseFloat(customer.shipping_fee);
        acc.totalRevenue += parseFloat(customer.revenue);
        acc.totalCost += parseFloat(customer.total_cost);
        acc.totalGrossProfit += parseFloat(customer.gross_profit);
        acc.totalPurchased += parseFloat(customer.total_purchased);
        return acc;
      },
      {
        totalCustomers: 0,
        totalQuantitySold: 0,
        totalActualQuantitySold: 0,
        totalOrderCount: 0,
        totalAmount: 0,
        totalReturnedAmount: 0,
        totalDiscountAmount: 0,
        totalShippingFee: 0,
        totalRevenue: 0,
        totalCost: 0,
        totalGrossProfit: 0,
        totalPurchased: 0,
      }
    );

    res.json({
      success: true,
      data: {
        businessActivity: businessActivity.map((customer) => ({
          customerId: customer.customer_id,
          customerName: customer.customer_name,
          customerPhone: customer.customer_phone,
          customerEmail: customer.customer_email,
          customerGroup: customer.customer_group || "Khách hàng thường",
          customerGroupCode: customer.customer_group_code || "NORMAL",
          quantitySold: parseInt(customer.quantity_sold),
          actualQuantitySold: parseInt(customer.actual_quantity_sold),
          orderCount: parseInt(customer.order_count),
          totalAmount: parseFloat(customer.total_amount),
          returnedAmount: parseFloat(customer.returned_amount),
          discountAmount: parseFloat(customer.discount_amount),
          shippingFee: parseFloat(customer.shipping_fee),
          revenue: parseFloat(customer.revenue),
          totalCost: parseFloat(customer.total_cost),
          grossProfit: parseFloat(customer.gross_profit),
          totalPurchased: parseFloat(customer.total_purchased),
          lastUpdated: customer.last_updated
            ? new Date(customer.last_updated).getTime()
            : null,
          status: "normal", // Có thể mở rộng logic phân loại trạng thái
        })),
        summary,
        period: {
          startDate: start.format("YYYY-MM-DD"),
          endDate: end.format("YYYY-MM-DD"),
        },
      },
    });
  } catch (error) {
    console.error("❌ Error in getBusinessActivityReport:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy báo cáo hoạt động kinh doanh",
      error: error.message,
    });
  }
};

// Báo cáo nhóm khách hàng
const getCustomerGroupReport = async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      limit = 20,
      // Bộ lọc nâng cao
      customerGroups,
      orderStatus,
      minOrderValue,
      maxOrderValue,
    } = req.query;

    console.log("👥 Getting customer group report...", {
      startDate,
      endDate,
      limit,
      filters: {
        customerGroups,
        orderStatus,
        minOrderValue,
        maxOrderValue,
      },
    });

    // Parse array parameters
    const parsedCustomerGroups = parseArrayParam(customerGroups);
    const parsedOrderStatus = parseArrayParam(orderStatus);

    const start = startDate
      ? dayjs(startDate).startOf("day")
      : dayjs().subtract(30, "days").startOf("day");
    const end = endDate ? dayjs(endDate).endOf("day") : dayjs().endOf("day");

    // Xây dựng điều kiện WHERE động cho nhóm khách hàng
    let whereConditions = [];
    let replacements = {
      startDate: start.toDate(),
      endDate: end.toDate(),
      limit: parseInt(limit),
    };

    // Bộ lọc nhóm khách hàng cụ thể
    if (parsedCustomerGroups.length > 0) {
      whereConditions.push(
        `nkh.id IN (${parsedCustomerGroups
          .map((_, i) => `:customerGroup${i}`)
          .join(", ")})`
      );
      parsedCustomerGroups.forEach((id, i) => {
        replacements[`customerGroup${i}`] = parseInt(id);
      });
    }

    // Bộ lọc trạng thái đơn hàng
    if (parsedOrderStatus.length > 0) {
      whereConditions.push(
        `dh.trang_thai IN (${parsedOrderStatus
          .map((_, i) => `:orderStatus${i}`)
          .join(", ")})`
      );
      parsedOrderStatus.forEach((status, i) => {
        replacements[`orderStatus${i}`] = status;
      });
    }

    // Bộ lọc giá trị đơn hàng
    if (minOrderValue) {
      whereConditions.push("dh.tong_phai_tra >= :minOrderValue");
      replacements.minOrderValue = parseFloat(minOrderValue);
    }
    if (maxOrderValue) {
      whereConditions.push("dh.tong_phai_tra <= :maxOrderValue");
      replacements.maxOrderValue = parseFloat(maxOrderValue);
    }

    // Tạo chuỗi WHERE động
    const dynamicWhere =
      whereConditions.length > 0 ? `AND ${whereConditions.join(" AND ")}` : "";

    // Báo cáo theo nhóm khách hàng thực tế từ database với chi tiết đầy đủ và bộ lọc
    const customerGroupsData = await sequelize.query(
      `
      SELECT
        nkh.ten_nhom as group_name,
        nkh.ma_nhom as group_code,
        COUNT(DISTINCT nkhnd.nguoi_dung_id) as customer_count,

        -- Số lượng hàng bán ra
        COALESCE(SUM(dhsp.so_luong), 0) as total_quantity_sold,

        -- Số lượng hàng thực bán (không tính hàng hoàn trả)
        COALESCE(SUM(CASE WHEN dh.trang_thai != 'hoan_hang' THEN dhsp.so_luong ELSE 0 END), 0) as actual_quantity_sold,

        -- Số đơn hàng
        COUNT(dh.id) as total_orders,

        -- Tiền hàng (tổng tiền trước chiết khấu)
        COALESCE(SUM(dh.tong_tien), 0) as total_amount,

        -- Tiền hàng trả lại
        COALESCE(SUM(CASE WHEN dh.trang_thai = 'hoan_hang' THEN dh.tong_tien ELSE 0 END), 0) as returned_amount,

        -- Tiền chiết khấu
        COALESCE(SUM(dh.chiet_khau), 0) as discount_amount,

        -- Phí giao hàng
        COALESCE(SUM(dh.phi_giao_hang), 0) as shipping_fee,

        -- Doanh thu (tổng phải trả)
        COALESCE(SUM(CASE WHEN dh.trang_thai != 'hoan_hang' THEN dh.tong_phai_tra ELSE 0 END), 0) as group_revenue,

        -- Chi phí ước tính
        COALESCE(SUM(dhsp.so_luong * COALESCE(pbsp.gia_nhap, 0)), 0) as total_cost,

        -- Lợi nhuận gộp
        COALESCE(SUM(CASE WHEN dh.trang_thai != 'hoan_hang' THEN dh.tong_phai_tra ELSE 0 END), 0) -
        COALESCE(SUM(dhsp.so_luong * COALESCE(pbsp.gia_nhap, 0)), 0) as gross_profit,

        COALESCE(AVG(dh.tong_phai_tra), 0) as avg_spent_per_customer

      FROM nhom_khach_hang nkh
      LEFT JOIN nhom_khach_hang_nguoi_dung nkhnd ON nkh.id = nkhnd.nhom_khach_hang_id
      LEFT JOIN don_hang dh ON nkhnd.nguoi_dung_id = dh.khach_hang_id
        AND dh.ngay_ban BETWEEN :startDate AND :endDate
        AND dh.trang_thai IN ('da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh', 'hoan_hang')
      LEFT JOIN don_hang_san_pham dhsp ON dh.id = dhsp.don_hang_id
      LEFT JOIN phien_ban_san_pham pbsp ON dhsp.phien_ban_san_pham_id = pbsp.id
      WHERE 1=1
      ${dynamicWhere}
      GROUP BY nkh.id, nkh.ten_nhom, nkh.ma_nhom
      ORDER BY group_revenue DESC
    `,
      {
        replacements,
        type: sequelize.QueryTypes.SELECT,
      }
    );

    // Thống kê tổng quan nhóm
    const groupSummary = {
      totalGroups: customerGroupsData.length,
      totalCustomers: customerGroupsData.reduce(
        (sum, group) => sum + parseInt(group.customer_count),
        0
      ),
      totalRevenue: customerGroupsData.reduce(
        (sum, group) => sum + parseFloat(group.group_revenue),
        0
      ),
      totalOrders: customerGroupsData.reduce(
        (sum, group) => sum + parseInt(group.total_orders),
        0
      ),
    };

    res.json({
      success: true,
      data: {
        customerGroups: customerGroupsData.map((group) => ({
          groupName: group.group_name,
          groupCode: group.group_code,
          customerCount: parseInt(group.customer_count),
          totalQuantitySold: parseInt(group.total_quantity_sold),
          actualQuantitySold: parseInt(group.actual_quantity_sold),
          totalOrders: parseInt(group.total_orders),
          totalAmount: parseFloat(group.total_amount),
          returnedAmount: parseFloat(group.returned_amount),
          discountAmount: parseFloat(group.discount_amount),
          shippingFee: parseFloat(group.shipping_fee),
          revenue: parseFloat(group.group_revenue),
          totalCost: parseFloat(group.total_cost),
          grossProfit: parseFloat(group.gross_profit),
          avgSpentPerCustomer: parseFloat(group.avg_spent_per_customer),
          color: getGroupColor(group.group_code || group.group_name),
        })),
        groupSummary,
        period: {
          startDate: start.format("YYYY-MM-DD"),
          endDate: end.format("YYYY-MM-DD"),
        },
      },
    });
  } catch (error) {
    console.error("❌ Error in getCustomerGroupReport:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy báo cáo nhóm khách hàng",
      error: error.message,
    });
  }
};

// Helper function để lấy màu cho từng nhóm
const getGroupColor = (groupCode) => {
  const colors = {
    VIP: "#ff4d4f",
    LOYAL: "#faad14",
    REGULAR: "#52c41a",
    NEW: "#1890ff",
    BUSINESS: "#722ed1",
    RETAIL: "#13c2c2",
    BANLE: "#52c41a",
    BANSI: "#fa8c16",
    test: "#8c8c8c",
    test1: "#f759ab",
    test2: "#a0d911",
  };
  return colors[groupCode] || "#8c8c8c";
};

module.exports = {
  getOverviewReport,
  getProductReport,
  getCustomerReport,
  getBusinessActivityReport,
  getCustomerGroupReport,
};
