import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Card, 
  Descriptions, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Typography, 
  Divider,
  Row,
  Col,
  Spin,
  Alert,
  message,
  Modal,
  Input
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  PrinterOutlined,
  DownloadOutlined,
  DollarOutlined,
  HistoryOutlined,
  RollbackOutlined
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { testAPI } from '../../services/api';
import { ORDER_STATUS_LABELS, ORDER_STATUS_COLORS } from '../../constants/orderStatus';
import { useCreateOrderPayment, useReturnOrder } from '../../hooks/useOrders';
import PaymentModal from '../../components/PaymentModal';

const { Title, Text } = Typography;
const { TextArea } = Input;

// Hook để lấy chi tiết đơn hàng
const useOrderDetail = (id) => {
  return useQuery(
    ['order', id],

    () => testAPI.getOrder(id),
    {
      enabled: !!id,
      retry: 1,
      refetchOnWindowFocus: false
    }
  );
};

const OrderDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { data, isLoading, error } = useOrderDetail(id);
  const createPaymentMutation = useCreateOrderPayment();
  const returnOrderMutation = useReturnOrder();

  // Payment modal state
  const [paymentModal, setPaymentModal] = React.useState({
    visible: false,
    orderData: null
  });

  // Return modal state
  const [returnModal, setReturnModal] = React.useState({
    visible: false,
    lyDoHoanHang: '',
    ghiChuHoanHang: ''
  });

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Lỗi"
        description={error.message}
        type="error"
        showIcon
        style={{ margin: '20px' }}
      />
    );
  }

  const order = data?.data;
  if (!order) {
    return (
      <Alert
        message="Không tìm thấy đơn hàng"
        type="warning"
        showIcon
        style={{ margin: '20px' }}
      />
    );
  }

  // Cấu hình cột cho bảng sản phẩm
  const productColumns = [
    {
      title: 'STT',
      key: 'index',
      width: 60,
      render: (_, __, index) => index + 1
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'ten_san_pham',
      key: 'ten_san_pham'
    },
    {
      title: 'Số lượng',
      dataIndex: 'so_luong',
      key: 'so_luong',
      width: 100,
      align: 'center'
    },
    {
      title: 'Đơn giá',
      dataIndex: 'don_gia',
      key: 'don_gia',
      width: 120,
      align: 'right',
      render: (value) => new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(value)
    },
    {
      title: 'Thành tiền',
      dataIndex: 'thanh_tien',
      key: 'thanh_tien',
      width: 120,
      align: 'right',
      render: (value) => new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(value)
    }
  ];

  // Handle payment
  const handlePayment = (orderData) => {
    // Chỉ cho phép thanh toán cho đơn hàng đã xác nhận và có khách hàng
    if (!orderData.khach_hang_id) {
      message.warning('Không thể thanh toán cho đơn hàng khách lẻ');
      return;
    }

    const confirmedStatuses = ['da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh'];
    if (!confirmedStatuses.includes(orderData.trang_thai)) {
      message.warning('Chỉ có thể thanh toán cho đơn hàng đã được xác nhận');
      return;
    }

    const remainingAmount = (orderData.tong_phai_tra || 0) - (orderData.tong_da_tra || 0);
    if (remainingAmount <= 0) {
      message.info('Đơn hàng này đã được thanh toán đầy đủ');
      return;
    }

    setPaymentModal({
      visible: true,
      orderData: orderData
    });
  };

  // Handle payment submit
  const handlePaymentSubmit = async (paymentData) => {
    try {
      await createPaymentMutation.mutateAsync(paymentData);
      setPaymentModal({ visible: false, orderData: null });
    } catch (error) {
      console.error('Payment error:', error);
    }
  };

  // Handle payment modal close
  const handlePaymentModalClose = () => {
    setPaymentModal({ visible: false, orderData: null });
  };

  // Handle return order
  const handleReturnOrder = () => {
    // Kiểm tra trạng thái có thể hoàn hàng (chỉ khi đã giao cho ĐVVC)
    if (order.trang_thai !== 'da_giao') {
      message.warning('Chỉ có thể hoàn hàng đơn đã giao cho đơn vị vận chuyển');
      return;
    }

    setReturnModal({
      visible: true,
      lyDoHoanHang: '',
      ghiChuHoanHang: ''
    });
  };

  // Handle return submit
  const handleReturnSubmit = async () => {
    if (!returnModal.lyDoHoanHang.trim()) {
      message.warning('Vui lòng nhập lý do hoàn hàng');
      return;
    }

    try {
      await returnOrderMutation.mutateAsync({
        id: order.id,
        returnData: {
          ly_do_hoan_hang: returnModal.lyDoHoanHang,
          ghi_chu_hoan_hang: returnModal.ghiChuHoanHang
        }
      });
      setReturnModal({ visible: false, lyDoHoanHang: '', ghiChuHoanHang: '' });
    } catch (error) {
      console.error('Return error:', error);
    }
  };

  // Handle return modal close
  const handleReturnModalClose = () => {
    setReturnModal({ visible: false, lyDoHoanHang: '', ghiChuHoanHang: '' });
  };

  // Lấy thông tin trạng thái
  const statusInfo = {
    label: ORDER_STATUS_LABELS[order.trang_thai] || 'Không xác định',
    color: ORDER_STATUS_COLORS[order.trang_thai] || 'default'
  };

  // Tính toán trạng thái thanh toán
  const totalAmount = order.tong_phai_tra || 0;
  const paidAmount = order.tong_da_tra || 0;
  const remainingAmount = totalAmount - paidAmount;
  const confirmedStatuses = ['da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh'];
  const canPay = order.khach_hang_id && confirmedStatuses.includes(order.trang_thai) && remainingAmount > 0;

  // Tính toán có thể hoàn hàng (chỉ khi đã giao cho ĐVVC)
  const canReturn = order.trang_thai === 'da_giao';

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/orders')}
          >
            Quay lại
          </Button>
          <Title level={3} style={{ margin: 0 }}>
            Chi tiết đơn hàng {order.ma_don_hang}
          </Title>
        </Space>
        
        <div style={{ float: 'right' }}>
          <Space>
            {canPay && (
              <Button
                type="primary"
                icon={<DollarOutlined />}
                onClick={() => handlePayment(order)}
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
              >
                Cập nhật thanh toán
              </Button>
            )}
            {canReturn && (
              <Button
                danger
                icon={<RollbackOutlined />}
                onClick={handleReturnOrder}
              >
                Hoàn hàng
              </Button>
            )}
            <Button icon={<PrinterOutlined />}>
              In đơn hàng
            </Button>
            <Button icon={<DownloadOutlined />}>
              Xuất PDF
            </Button>
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => navigate(`/orders/${id}/edit`)}
            >
              Chỉnh sửa
            </Button>
          </Space>
        </div>
        <div style={{ clear: 'both' }} />
      </div>

      <Row gutter={[24, 24]}>
        {/* Thông tin đơn hàng */}
        <Col span={16}>
          <Card title="Thông tin đơn hàng" style={{ marginBottom: '24px' }}>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="Mã đơn hàng" span={1}>
                <Text strong>{order.ma_don_hang}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="Trạng thái" span={1}>
                <Tag color={statusInfo.color}>{statusInfo.label}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Ngày bán" span={1}>
                {new Date(order.ngay_ban).toLocaleDateString('vi-VN')}
              </Descriptions.Item>
              <Descriptions.Item label="Nguồn đơn hàng" span={1}>
                {order.nguon_don_hang || 'Trực tiếp'}
              </Descriptions.Item>
              <Descriptions.Item label="Người tạo" span={1}>
                {order.nguoi_tao}
              </Descriptions.Item>
              <Descriptions.Item label="Nhân viên bán" span={1}>
                {order.nhan_vien_ban || 'Chưa phân công'}
              </Descriptions.Item>
              <Descriptions.Item label="Ghi chú" span={2}>
                {order.ghi_chu || 'Không có ghi chú'}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* Danh sách sản phẩm */}
          <Card title="Danh sách sản phẩm">
            <Table
              columns={productColumns}
              dataSource={order.san_pham_list || []}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>

        {/* Thông tin khách hàng và thanh toán */}
        <Col span={8}>
          {/* Thông tin khách hàng */}
          <Card title="Thông tin khách hàng" style={{ marginBottom: '24px' }}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Tên khách hàng">
                <Text strong>{order.khach_hang?.ho_ten}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="Số điện thoại">
                {order.khach_hang?.so_dien_thoai}
              </Descriptions.Item>
              <Descriptions.Item label="Email">
                {order.khach_hang?.email || 'Chưa có'}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* Thông tin thanh toán */}
          <Card
            title={
              <Space>
                <span>Thông tin thanh toán</span>
                {remainingAmount <= 0 ? (
                  <Tag color="green">Đã thanh toán đủ</Tag>
                ) : (
                  <Tag color="orange">Còn nợ</Tag>
                )}
              </Space>
            }
            extra={
              canPay && (
                <Button
                  type="link"
                  size="small"
                  icon={<DollarOutlined />}
                  onClick={() => handlePayment(order)}
                >
                  Cập nhật thanh toán
                </Button>
              )
            }
          >
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Tổng tiền hàng">
                <Text strong>
                  {new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                  }).format(order.tong_tien)}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="Chiết khấu">
                <Text>
                  {new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                  }).format(order.chiet_khau || 0)}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="Tổng phải trả">
                <Text strong style={{ fontSize: '16px', color: '#1890ff' }}>
                  {new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                  }).format(order.tong_phai_tra)}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="Đã thanh toán">
                <Text style={{ color: paidAmount > 0 ? '#52c41a' : 'inherit' }}>
                  {new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                  }).format(order.tong_da_tra || 0)}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="Còn phải trả">
                <Text strong style={{
                  color: remainingAmount > 0 ? '#ff4d4f' : '#52c41a',
                  fontSize: '16px'
                }}>
                  {new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                  }).format(remainingAmount)}
                </Text>
              </Descriptions.Item>

              {/* Trạng thái thanh toán */}
              <Descriptions.Item label="Trạng thái thanh toán">
                {remainingAmount <= 0 ? (
                  <Tag color="green" style={{ fontSize: '14px' }}>
                    ✓ Đã thanh toán đầy đủ
                  </Tag>
                ) : paidAmount > 0 ? (
                  <Tag color="orange" style={{ fontSize: '14px' }}>
                    ⚠ Thanh toán một phần
                  </Tag>
                ) : (
                  <Tag color="red" style={{ fontSize: '14px' }}>
                    ✗ Chưa thanh toán
                  </Tag>
                )}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>

      {/* Payment Modal */}
      <PaymentModal
        visible={paymentModal.visible}
        orderData={paymentModal.orderData}
        onCancel={handlePaymentModalClose}
        onSubmit={handlePaymentSubmit}
        loading={createPaymentMutation.isLoading}
      />

      {/* Return Order Modal */}
      <Modal
        title="Hoàn hàng"
        open={returnModal.visible}
        onOk={handleReturnSubmit}
        onCancel={handleReturnModalClose}
        confirmLoading={returnOrderMutation.isLoading}
        okText="Xác nhận hoàn hàng"
        cancelText="Hủy"
        okButtonProps={{ danger: true }}
        width={600}
      >
        <div style={{ marginBottom: '16px' }}>
          <Text strong>Đơn hàng: </Text>
          <Text>{order?.ma_don_hang}</Text>
        </div>
        <div style={{ marginBottom: '16px' }}>
          <Text strong>Trạng thái hiện tại: </Text>
          <Tag color={statusInfo.color}>{statusInfo.label}</Tag>
        </div>

        <div style={{ marginBottom: '16px' }}>
          <Text strong style={{ color: 'red' }}>Lý do hoàn hàng *</Text>
          <Input
            placeholder="Nhập lý do hoàn hàng..."
            value={returnModal.lyDoHoanHang}
            onChange={(e) => setReturnModal(prev => ({ ...prev, lyDoHoanHang: e.target.value }))}
            style={{ marginTop: '8px' }}
          />
        </div>

        <div style={{ marginBottom: '16px' }}>
          <Text strong>Ghi chú thêm</Text>
          <TextArea
            placeholder="Ghi chú thêm về việc hoàn hàng..."
            value={returnModal.ghiChuHoanHang}
            onChange={(e) => setReturnModal(prev => ({ ...prev, ghiChuHoanHang: e.target.value }))}
            rows={3}
            style={{ marginTop: '8px' }}
          />
        </div>

        <Alert
          message="Lưu ý"
          description="Hoàn hàng sẽ cập nhật lại tồn kho và điều chỉnh công nợ khách hàng. Thao tác này không thể hoàn tác."
          type="warning"
          showIcon
          style={{ marginTop: '16px' }}
        />
      </Modal>
    </div>
  );
};

export default OrderDetail;
