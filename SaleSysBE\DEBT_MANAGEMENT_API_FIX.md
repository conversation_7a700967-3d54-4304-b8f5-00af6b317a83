# 🔧 Sửa API Quản Lý Công Nợ

## ❗ **Vấn đề:**
API quản lý công nợ vẫn đang sử dụng logic cũ:
- Tính overdue debt bằng `(tong_phai_tra - tong_da_tra)`
- Hiển thị `debt_amount` bằng `(tong_phai_tra - tong_da_tra)`
- Timeline hiển thị `tong_phai_tra` thay vì thông tin chi tiết

## ✅ **Logic mới (ĐÚNG):**
Quản lý công nợ sẽ sử dụng **`con_phai_tra`** làm nguồn chính để tính toán công nợ.

### **Công thức:**
- **Tổng công nợ** = Tổng `con_phai_tra` của tất cả đơn hàng
- **Công nợ quá hạn** = Tổng `con_phai_tra` của đơn hàng > 30 ngày
- **Payment status** = So sánh `tong_da_tra` với `tien_cod`

## 🔧 **Những thay đổi đã thực hiện:**

### **File: SaleSysBE/controllers/debtController.js**

#### **1. Sửa tính overdue debt (Line 104-113):**

**Trước (SAI):**
```javascript
if (
  daysDiff > 30 &&
  (order.tong_phai_tra || 0) - (order.tong_da_tra || 0) > 0
) {
  return sum + ((order.tong_phai_tra || 0) - (order.tong_da_tra || 0));
}
```

**Sau (ĐÚNG):**
```javascript
if (
  daysDiff > 30 &&
  (order.con_phai_tra || 0) > 0
) {
  return sum + (order.con_phai_tra || 0);
}
```

#### **2. Sửa getCustomerDebtDetail (Line 461-476):**

**Trước (SAI):**
```javascript
const orderData = orders.map((order) => ({
  total_amount: order.tong_phai_tra,
  paid_amount: order.tong_da_tra || 0,
  debt_amount: (order.tong_phai_tra || 0) - (order.tong_da_tra || 0),
  payment_status: (order.tong_da_tra || 0) >= (order.tong_phai_tra || 0) ? "paid" : ...
}));
```

**Sau (ĐÚNG):**
```javascript
const orderData = orders.map((order) => ({
  total_amount: order.tong_tien, // Tổng tiền sản phẩm
  cod_amount: order.tien_cod || 0, // Tiền COD
  paid_amount: order.tong_da_tra || 0, // Đã thanh toán
  debt_amount: order.con_phai_tra || 0, // Công nợ (từ con_phai_tra)
  payment_status: (order.tong_da_tra || 0) >= (order.tien_cod || 0) ? "paid" : ...
}));
```

#### **3. Sửa totalPurchased (Line 457-459):**

**Trước (SAI):**
```javascript
const totalPurchased = orders.reduce((sum, order) => {
  return sum + (order.tong_phai_tra || 0);
}, 0);
```

**Sau (ĐÚNG):**
```javascript
const totalPurchased = orders.reduce((sum, order) => {
  return sum + (order.tong_tien || 0); // Sử dụng tong_tien (tổng tiền sản phẩm)
}, 0);
```

#### **4. Cập nhật timeline description (Line 483-495):**

**Trước (SAI):**
```javascript
description: `Tổng tiền: ${order.tong_phai_tra.toLocaleString()}`
```

**Sau (ĐÚNG):**
```javascript
description: `Tổng tiền: ${order.tong_tien.toLocaleString()} | COD: ${order.tien_cod.toLocaleString()} | Công nợ: ${order.con_phai_tra.toLocaleString()}`
```

## 📊 **Ví dụ cụ thể:**

### **Đơn hàng:**
- Sản phẩm: 1,000,000đ
- Tiền cọc: 200,000đ
- Tiền COD: 500,000đ
- Đã thanh toán: 300,000đ

### **Kết quả API (MỚI - ĐÚNG):**
```json
{
  "order_data": {
    "total_amount": 1000000,     // tong_tien (sản phẩm)
    "cod_amount": 500000,        // tien_cod
    "paid_amount": 300000,       // tong_da_tra
    "debt_amount": 300000,       // con_phai_tra (1M - 500K - 200K)
    "payment_status": "partial"  // 300K < 500K (COD)
  },
  "timeline": {
    "description": "Tổng tiền: 1,000,000đ | COD: 500,000đ | Công nợ: 300,000đ"
  }
}
```

### **Trước đây (CŨ - SAI):**
```json
{
  "order_data": {
    "total_amount": 500000,      // tong_phai_tra (COD)
    "paid_amount": 300000,       // tong_da_tra
    "debt_amount": 200000,       // tong_phai_tra - tong_da_tra (SAI!)
    "payment_status": "partial"  // 300K < 500K
  },
  "timeline": {
    "description": "Tổng tiền: 500,000đ"  // Chỉ hiển thị COD
  }
}
```

## 🎯 **Ý nghĩa thay đổi:**

### **1. Debt Amount chính xác:**
- **Trước:** `debt_amount` = 200,000đ (SAI - chỉ tính thiếu COD)
- **Sau:** `debt_amount` = 300,000đ (ĐÚNG - công nợ thực tế)

### **2. Payment Status đúng:**
- **Trước:** So sánh với `tong_phai_tra` (COD)
- **Sau:** Vẫn so sánh với `tien_cod` (đúng cho thanh toán)

### **3. Timeline chi tiết:**
- **Trước:** Chỉ hiển thị COD
- **Sau:** Hiển thị đầy đủ: Tổng tiền + COD + Công nợ

## 🧪 **Cách test:**

### **Test Case 1: API getDebtList**
1. Gọi `GET /api/debt`
2. ✅ **Kiểm tra:** `total_debt` = tổng `con_phai_tra`
3. ✅ **Kiểm tra:** `overdue_debt` sử dụng `con_phai_tra`

### **Test Case 2: API getCustomerDebtDetail**
1. Gọi `GET /api/debt/customer/{id}`
2. ✅ **Kiểm tra:** `debt_amount` = `con_phai_tra`
3. ✅ **Kiểm tra:** `total_amount` = `tong_tien`
4. ✅ **Kiểm tra:** Timeline hiển thị đầy đủ thông tin

### **Test Case 3: API syncDebtFromOrders**
1. Gọi `POST /api/debt/sync`
2. ✅ **Kiểm tra:** Sử dụng `con_phai_tra` để tính tổng
3. ✅ **Kiểm tra:** Cập nhật đúng vào `cong_no_nguoi_dung`

## 🔄 **Luồng hoạt động:**

### **1. Tạo đơn hàng:**
```
Đơn hàng mới → con_phai_tra = tong_tien - tien_cod - tien_coc
```

### **2. Sync công nợ:**
```
syncDebtFromOrders() → Tổng con_phai_tra → cong_no_nguoi_dung.tong_cong_no
```

### **3. Hiển thị công nợ:**
```
getDebtList() → Lấy từ cong_no_nguoi_dung.tong_cong_no (ưu tiên)
              → Fallback: Tổng con_phai_tra từ đơn hàng
```

### **4. Chi tiết khách hàng:**
```
getCustomerDebtDetail() → debt_amount = con_phai_tra
                       → payment_status = so sánh tong_da_tra vs tien_cod
```

## 🚀 **Tổng kết:**

API quản lý công nợ đã được cập nhật để:
- ✅ **Sử dụng `con_phai_tra`** làm nguồn chính cho công nợ
- ✅ **Hiển thị đúng thông tin** trong chi tiết đơn hàng
- ✅ **Timeline chi tiết** với đầy đủ thông tin
- ✅ **Payment status chính xác** dựa trên COD
- ✅ **Overdue debt đúng** theo logic mới
- ✅ **Sync debt chính xác** từ đơn hàng

Bây giờ trang quản lý công nợ sẽ hiển thị đúng công nợ thực tế theo logic mới! 🎉
