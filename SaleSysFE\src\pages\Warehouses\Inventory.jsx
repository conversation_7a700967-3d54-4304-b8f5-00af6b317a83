import React, { useState, useMemo, useEffect } from "react";
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Row,
  Col,
  Statistic,
  Tooltip,
  Modal,
  Form,
  InputNumber,
  Alert,
  Spin,
} from "antd";
import {
  SearchOutlined,
  InboxOutlined,
  WarningOutlined,
  EditOutlined,
  ExportOutlined,
  ImportOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import {
  useInventory,
  useAdjustInventory,
  useWarehouses,
} from "../../hooks/useWarehouses";

const { Title, Text } = Typography;
const { Option } = Select;

const Inventory = () => {
  const [searchText, setSearchText] = useState("");
  const [debouncedSearchText, setDebouncedSearchText] = useState("");
  const [selectedWarehouse, setSelectedWarehouse] = useState("all");
  const [stockFilter, setStockFilter] = useState("all");
  const [isAdjustModalVisible, setIsAdjustModalVisible] = useState(false);
  const [adjustingItem, setAdjustingItem] = useState(null);
  const [form] = Form.useForm();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Debounce search text
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(searchText);
      setCurrentPage(1); // Reset to first page when searching
    }, 500);

    return () => clearTimeout(timer);
  }, [searchText]);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedWarehouse, stockFilter]);

  // API hooks
  const { data: warehouses = [], isLoading: warehousesLoading } =
    useWarehouses();
  const {
    data: inventoryResponse = { data: [], statistics: {}, pagination: {} },
    isLoading: inventoryLoading,
    refetch: refetchInventory,
  } = useInventory({
    warehouse_id: selectedWarehouse !== "all" ? selectedWarehouse : undefined,
    page: currentPage,
    limit: pageSize,
    search: debouncedSearchText,
    stock_filter: stockFilter,
  });
  const adjustInventoryMutation = useAdjustInventory();

  // Extract data from response
  const inventory = inventoryResponse.data || [];
  const statistics = inventoryResponse.statistics || {};
  const pagination = inventoryResponse.pagination || {};

  // Since filtering is now done on server-side, we just use the inventory data directly
  const filteredInventory = inventory;

  // Check if any data is loading
  const isLoading = inventoryLoading || warehousesLoading;

  const handleAdjustStock = (item) => {
    setAdjustingItem(item);
    form.setFieldsValue({
      current_stock: item.ton_kho,
      current_min_stock: item.ton_kho_toi_thieu,
      adjustment_type: "set",
      adjustment_value: item.ton_kho,
      min_stock_value: item.ton_kho_toi_thieu,
      cost_price: item.gia_von || 0,
      warehouse_id:
        item.kho_id || (warehouses.length > 0 ? warehouses[0].id : null),
      reason: "",
    });
    setIsAdjustModalVisible(true);
  };

  const handleSubmitAdjustment = async (values) => {
    try {
      let newStock = adjustingItem.ton_kho;

      if (values.adjustment_type === "set") {
        newStock = values.adjustment_value;
      } else if (values.adjustment_type === "add") {
        newStock = adjustingItem.ton_kho + values.adjustment_value;
      } else if (values.adjustment_type === "subtract") {
        newStock = adjustingItem.ton_kho - values.adjustment_value;
      }

      const adjustmentData = {
        inventory_id: adjustingItem.id,
        phien_ban_id: adjustingItem.phien_ban_id,
        kho_id: adjustingItem.kho_id || values.warehouse_id,
        adjustment_type: values.adjustment_type,
        adjustment_value: values.adjustment_value,
        new_stock: Math.max(0, newStock),
        new_min_stock: values.min_stock_value,
        new_cost_price: values.cost_price,
        reason: values.reason,
      };

      await adjustInventoryMutation.mutateAsync(adjustmentData);

      setIsAdjustModalVisible(false);
      form.resetFields();
      setAdjustingItem(null);
      refetchInventory();
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  const getStockStatus = (current, minimum) => {
    if (current === 0) {
      return { status: "error", text: "Hết hàng", color: "red" };
    } else if (current <= minimum) {
      return { status: "warning", text: "Sắp hết", color: "orange" };
    } else {
      return { status: "success", text: "Bình thường", color: "green" };
    }
  };

  const columns = [
    {
      title: "Sản phẩm / Phiên bản",
      key: "product",
      width: 280,
      render: (_, record) => (
        <div>
          <Text strong style={{ fontSize: "14px", color: "#333" }}>
            📦 {record.phien_ban}
          </Text>
          <br />
          <Text strong style={{ color: "#1890ff", fontSize: "13px" }}>
            {record.san_pham}
          </Text>
          <br />
          <div style={{ marginTop: 4 }}>
            <Tag color="blue" size="small">
              SKU: {record.ma_sku}
            </Tag>
          </div>
        </div>
      ),
    },
    {
      title: "Kho hàng",
      dataIndex: "ten_kho",
      key: "ten_kho",
      width: 150,
    },
    {
      title: "Tồn kho",
      key: "stock",
      width: 120,
      align: "center",
      render: (_, record) => {
        const status = getStockStatus(record.ton_kho, record.ton_kho_toi_thieu);
        return (
          <div>
            <Text strong style={{ color: status.color }}>
              {record.ton_kho}
            </Text>
            <br />
            <Tag color={status.color} size="small">
              {status.text}
            </Tag>
          </div>
        );
      },
    },
    {
      title: "Tồn kho tối thiểu",
      dataIndex: "ton_kho_toi_thieu",
      key: "ton_kho_toi_thieu",
      width: 120,
      align: "center",
    },
    {
      title: "Giá vốn",
      key: "gia_von",
      width: 140,
      align: "right",
      render: (_, record) => {
        const giaVonDonVi = record.gia_von || 0;
        const tongGiaTri = (record.ton_kho || 0) * giaVonDonVi;
        return (
          <div>
            <Text style={{ fontSize: "12px", color: "#666" }}>
              {giaVonDonVi.toLocaleString("vi-VN")} đ/sp
            </Text>
            <br />
            <Text strong style={{ color: "#52c41a" }}>
              {tongGiaTri.toLocaleString("vi-VN")} đ
            </Text>
          </div>
        );
      },
    },
    {
      title: "Giá bán",
      dataIndex: "gia_ban",
      key: "gia_ban",
      width: 120,
      align: "right",
      render: (value) => value?.toLocaleString("vi-VN") + " đ",
    },
    {
      title: "Cập nhật",
      dataIndex: "ngay_cap_nhat",
      key: "ngay_cap_nhat",
      width: 120,
      render: (date) => new Date(date).toLocaleDateString("vi-VN"),
    },
    {
      title: "Thao tác",
      key: "actions",
      width: 100,
      align: "center",
      render: (_, record) => (
        <Tooltip title="Điều chỉnh tồn kho">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleAdjustStock(record)}
            size="small"
          />
        </Tooltip>
      ),
    },
  ];

  // Calculate statistics - each variant is treated as separate product
  const safeInventory = Array.isArray(inventory) ? inventory : [];
  const totalItems = statistics.total_variants || safeInventory.length;
  const lowStockItems =
    statistics.low_stock_variants ||
    safeInventory.filter(
      (item) =>
        (item?.ton_kho || 0) <= (item?.ton_kho_toi_thieu || 0) &&
        (item?.ton_kho || 0) > 0
    ).length;
  const outOfStockItems =
    statistics.out_of_stock_variants ||
    safeInventory.filter((item) => (item?.ton_kho || 0) === 0).length;
  const totalValue =
    statistics.total_inventory_value ||
    safeInventory.reduce(
      (sum, item) => sum + (item?.ton_kho || 0) * (item?.gia_von || 0),
      0
    );
  const uniqueProducts = statistics.unique_products || 0;

  // Show loading spinner
  if (isLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "400px",
        }}
      >
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      {/* Alert for low stock */}
      {lowStockItems > 0 && (
        <Alert
          message={`Có ${lowStockItems} sản phẩm sắp hết hàng hoặc đã hết hàng`}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
          action={
            <Button size="small" onClick={() => setStockFilter("low")}>
              Xem chi tiết
            </Button>
          }
        />
      )}

      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={4}>
          <Card size="small">
            <Statistic
              title="Sản phẩm gốc"
              value={uniqueProducts}
              prefix="🏷️"
              valueStyle={{ color: "#722ed1", fontSize: "20px" }}
            />
            <Text type="secondary" style={{ fontSize: "11px" }}>
              Số sản phẩm chính
            </Text>
          </Card>
        </Col>
        <Col span={5}>
          <Card size="small">
            <Statistic
              title="Tổng phiên bản"
              value={totalItems}
              prefix={<InboxOutlined />}
              valueStyle={{ color: "#1890ff", fontSize: "20px" }}
            />
            <Text type="secondary" style={{ fontSize: "11px" }}>
              Mỗi phiên bản quản lý riêng
            </Text>
          </Card>
        </Col>
        <Col span={5}>
          <Card size="small">
            <Statistic
              title="Sắp hết hàng"
              value={lowStockItems}
              prefix={<WarningOutlined />}
              valueStyle={{ color: "#fa8c16", fontSize: "20px" }}
            />
            <Text type="secondary" style={{ fontSize: "11px" }}>
              Phiên bản cần nhập thêm
            </Text>
          </Card>
        </Col>
        <Col span={5}>
          <Card size="small">
            <Statistic
              title="Hết hàng"
              value={outOfStockItems}
              prefix={<WarningOutlined />}
              valueStyle={{ color: "#ff4d4f", fontSize: "20px" }}
            />
            <Text type="secondary" style={{ fontSize: "11px" }}>
              Phiên bản cần nhập ngay
            </Text>
          </Card>
        </Col>
        <Col span={5}>
          <Card size="small">
            <Statistic
              title="Giá trị tồn"
              value={totalValue}
              prefix="₫"
              valueStyle={{ color: "#52c41a", fontSize: "18px" }}
              formatter={(value) => value?.toLocaleString("vi-VN")}
            />
            <Text type="secondary" style={{ fontSize: "11px" }}>
              Tổng giá trị theo giá vốn
            </Text>
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col>
              <Title level={4} style={{ margin: 0 }}>
                Quản lý tồn kho
              </Title>
            </Col>
            <Col flex="auto">
              <Space style={{ float: "right" }}>
                <Input
                  placeholder="Tìm kiếm sản phẩm..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: 250 }}
                  allowClear
                />
                <Select
                  value={selectedWarehouse}
                  onChange={setSelectedWarehouse}
                  style={{ width: 150 }}
                >
                  <Option value="all">Tất cả kho</Option>
                  {Array.isArray(warehouses) &&
                    warehouses.map((warehouse) => (
                      <Option key={warehouse?.id} value={warehouse?.id}>
                        {warehouse?.ten_kho}
                      </Option>
                    ))}
                </Select>
                <Select
                  value={stockFilter}
                  onChange={setStockFilter}
                  style={{ width: 150 }}
                >
                  <Option value="all">Tất cả</Option>
                  <Option value="normal">Bình thường</Option>
                  <Option value="low">Sắp hết</Option>
                  <Option value="out">Hết hàng</Option>
                </Select>
                <Button icon={<SyncOutlined />} onClick={refetchInventory}>
                  Làm mới
                </Button>
                <Button icon={<ExportOutlined />}>Xuất Excel</Button>
                <Button type="primary" icon={<ImportOutlined />}>
                  Nhập Excel
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={filteredInventory}
          loading={isLoading || adjustInventoryMutation.isLoading}
          rowKey="id"
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: pagination.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} phiên bản sản phẩm`,
            onChange: (page, size) => {
              setCurrentPage(page);
              if (size !== pageSize) {
                setPageSize(size);
                setCurrentPage(1); // Reset to first page when changing page size
              }
            },
            onShowSizeChange: (current, size) => {
              setPageSize(size);
              setCurrentPage(1); // Reset to first page when changing page size
            },
            pageSizeOptions: ["10", "20", "50", "100"],
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* Stock Adjustment Modal */}
      <Modal
        title="Điều chỉnh tồn kho"
        open={isAdjustModalVisible}
        onCancel={() => {
          setIsAdjustModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={500}
      >
        {adjustingItem && (
          <div
            style={{
              marginBottom: 16,
              padding: 12,
              background: "#f5f5f5",
              borderRadius: 6,
              border: "1px solid #d9d9d9",
            }}
          >
            <Text strong style={{ fontSize: "14px", color: "#333" }}>
              📦 {adjustingItem.phien_ban}
            </Text>
            <br />
            <Text strong style={{ color: "#1890ff", fontSize: "13px" }}>
              {adjustingItem.san_pham}
            </Text>
            <br />
            <Tag color="blue" size="small" style={{ marginTop: 4 }}>
              SKU: {adjustingItem.ma_sku}
            </Tag>
            <br />
            <Text type="secondary" style={{ marginTop: 8, display: "block" }}>
              🏪 Kho hàng: {adjustingItem.ten_kho}
            </Text>
            <Text type="secondary">
              📦 Tồn kho hiện tại:{" "}
              <Text strong style={{ color: "#52c41a" }}>
                {adjustingItem.ton_kho}
              </Text>
            </Text>
          </div>
        )}

        <Form form={form} layout="vertical" onFinish={handleSubmitAdjustment}>
          {adjustingItem && adjustingItem.kho_id === null && (
            <Form.Item
              name="warehouse_id"
              label="Chọn kho hàng"
              rules={[{ required: true, message: "Vui lòng chọn kho hàng" }]}
            >
              <Select placeholder="Chọn kho hàng để khởi tạo tồn kho">
                {Array.isArray(warehouses) &&
                  warehouses.map((warehouse) => (
                    <Option key={warehouse.id} value={warehouse.id}>
                      {warehouse.ten_kho}
                    </Option>
                  ))}
              </Select>
            </Form.Item>
          )}

          <Form.Item
            name="adjustment_type"
            label="Loại điều chỉnh"
            rules={[
              { required: true, message: "Vui lòng chọn loại điều chỉnh" },
            ]}
          >
            <Select>
              <Option value="set">Đặt số lượng mới</Option>
              <Option value="add">Nhập thêm</Option>
              <Option value="subtract">Xuất bớt</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="adjustment_value"
            label="Số lượng"
            rules={[
              { required: true, message: "Vui lòng nhập số lượng" },
              {
                type: "number",
                min: 0,
                message: "Số lượng phải lớn hơn hoặc bằng 0",
              },
            ]}
          >
            <InputNumber
              style={{ width: "100%" }}
              min={0}
              placeholder="Nhập số lượng"
            />
          </Form.Item>

          <Form.Item
            name="min_stock_value"
            label="Tồn kho tối thiểu"
            rules={[
              {
                type: "number",
                min: 0,
                message: "Tồn kho tối thiểu phải lớn hơn hoặc bằng 0",
              },
            ]}
          >
            <InputNumber
              style={{ width: "100%" }}
              min={0}
              placeholder="Nhập tồn kho tối thiểu"
            />
          </Form.Item>

          <Form.Item
            name="cost_price"
            label="Giá vốn (đ/sản phẩm)"
            rules={[
              {
                type: "number",
                min: 0,
                message: "Giá vốn phải lớn hơn hoặc bằng 0",
              },
            ]}
          >
            <InputNumber
              style={{ width: "100%" }}
              min={0}
              placeholder="Nhập giá vốn"
              formatter={(value) =>
                `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
              }
              parser={(value) => value.replace(/\$\s?|(,*)/g, "")}
            />
          </Form.Item>

          <Form.Item
            name="reason"
            label="Lý do điều chỉnh"
            rules={[
              { required: true, message: "Vui lòng nhập lý do điều chỉnh" },
            ]}
          >
            <Input.TextArea
              rows={3}
              placeholder="Nhập lý do điều chỉnh tồn kho"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: "right" }}>
            <Space>
              <Button onClick={() => setIsAdjustModalVisible(false)}>
                Hủy
              </Button>
              <Button type="primary" htmlType="submit">
                Điều chỉnh
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Inventory;
