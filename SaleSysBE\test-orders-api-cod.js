require('dotenv').config();
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, sequelize } = require('./models');

async function testOrdersAPICOD() {
  try {
    console.log('🧪 Testing Orders API COD Fields...\n');

    // 1. Tạo khách hàng test
    console.log('1️⃣ Creating test customer...');
    const customer = await NguoiDung.create({
      ho_ten: 'Nguyễn Văn Test COD',
      so_dien_thoai: '0987654999',
      email: '<EMAIL>',
      loai_nguoi_dung: 'khach_hang',
      trang_thai: 'dang_giao_dich'
    });
    console.log(`✅ Created customer: ${customer.ho_ten} (ID: ${customer.id})`);

    // 2. Tạo đơn hàng có tiền COD
    console.log('\n2️⃣ Creating order with COD...');
    const order = await DonHang.create({
      ma_don_hang: `COD-TEST-${Date.now()}`,
      khach_hang_id: customer.id,
      ngay_ban: new Date(),
      trang_thai: 'da_xac_nhan',
      tong_tien: 1000000, // Tổng tiền sản phẩm
      tong_phai_tra: 500000, // Tiền COD
      tong_da_tra: 200000, // Đã thanh toán
      con_phai_tra: 300000, // Còn nợ
      tien_cod: 500000, // Tiền COD
      tien_coc: 200000, // Tiền cọc
      ghi_chu: 'Test order with COD fields'
    });
    console.log(`✅ Created order: ${order.ma_don_hang}`);
    console.log(`   - tong_tien: ${order.tong_tien?.toLocaleString('vi-VN')}đ`);
    console.log(`   - tien_cod: ${order.tien_cod?.toLocaleString('vi-VN')}đ`);
    console.log(`   - tien_coc: ${order.tien_coc?.toLocaleString('vi-VN')}đ`);

    // 3. Test API getOrders (simulate)
    console.log('\n3️⃣ Testing getOrders API format...');
    
    // Simulate getOrders logic
    const orders = await DonHang.findAll({
      where: { id: order.id },
      include: [
        {
          model: NguoiDung,
          as: "khachHang",
          attributes: ["ho_ten", "so_dien_thoai"],
          required: false,
        }
      ]
    });

    const formattedOrders = orders.map((order) => {
      const orderTotalAmount = order.tong_phai_tra || 0;
      const orderPaidAmount = order.tong_da_tra || 0;
      const remainingAmount = order.con_phai_tra || 0;

      return {
        id: order.id,
        ma_don_hang: order.ma_don_hang,
        khach_hang_id: order.khach_hang_id,
        ten_khach_hang: order.khachHang?.ho_ten || "Khách lẻ",
        so_dien_thoai: order.khachHang?.so_dien_thoai,
        ngay_ban: order.ngay_ban,
        trang_thai: order.trang_thai,
        tong_tien: order.tong_tien,
        chiet_khau: order.chiet_khau,
        tong_phai_tra: orderTotalAmount,
        tong_da_tra: orderPaidAmount,
        con_phai_tra: remainingAmount,
        tien_cod: order.tien_cod || 0, // Trường mới
        tien_coc: order.tien_coc || 0, // Trường mới
        ghi_chu: order.ghi_chu,
        so_luong_san_pham: 0,
        nguoi_tao: order.nguoi_tao,
      };
    });

    console.log('📊 Formatted Order Data:');
    const testOrder = formattedOrders[0];
    console.log(`   - ma_don_hang: ${testOrder.ma_don_hang}`);
    console.log(`   - tong_tien: ${testOrder.tong_tien?.toLocaleString('vi-VN')}đ`);
    console.log(`   - tien_cod: ${testOrder.tien_cod?.toLocaleString('vi-VN')}đ`);
    console.log(`   - tien_coc: ${testOrder.tien_coc?.toLocaleString('vi-VN')}đ`);
    console.log(`   - tong_da_tra: ${testOrder.tong_da_tra?.toLocaleString('vi-VN')}đ`);

    // 4. Test COD calculation logic
    console.log('\n4️⃣ Testing COD calculation logic...');
    const codAmount = testOrder.tien_cod || 0;
    const paidAmount = testOrder.tong_da_tra || 0;
    const remainingCOD = codAmount - paidAmount;

    console.log(`   COD Calculation:`);
    console.log(`   - Tiền COD: ${codAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - Đã thanh toán: ${paidAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - Còn COD: ${remainingCOD.toLocaleString('vi-VN')}đ`);

    // 5. Test getOrder (detail) API format
    console.log('\n5️⃣ Testing getOrder detail API format...');
    
    const orderDetail = await DonHang.findByPk(order.id, {
      include: [
        {
          model: NguoiDung,
          as: "khachHang",
          attributes: ["ho_ten", "so_dien_thoai", "email"],
          required: false,
        }
      ]
    });

    const formattedOrderDetail = {
      id: orderDetail.id,
      ma_don_hang: orderDetail.ma_don_hang,
      khach_hang: {
        ho_ten: orderDetail.khachHang?.ho_ten || "Khách lẻ",
        so_dien_thoai: orderDetail.khachHang?.so_dien_thoai,
        email: orderDetail.khachHang?.email,
      },
      ngay_ban: orderDetail.ngay_ban,
      trang_thai: orderDetail.trang_thai,
      tong_tien: orderDetail.tong_tien,
      chiet_khau: orderDetail.chiet_khau,
      tong_phai_tra: orderDetail.tong_phai_tra,
      tong_da_tra: orderDetail.tong_da_tra,
      con_phai_tra: orderDetail.con_phai_tra,
      tien_cod: orderDetail.tien_cod || 0, // Trường mới
      tien_coc: orderDetail.tien_coc || 0, // Trường mới
      ghi_chu: orderDetail.ghi_chu,
      san_pham_list: []
    };

    console.log('📋 Order Detail Data:');
    console.log(`   - ma_don_hang: ${formattedOrderDetail.ma_don_hang}`);
    console.log(`   - tong_tien: ${formattedOrderDetail.tong_tien?.toLocaleString('vi-VN')}đ`);
    console.log(`   - tien_cod: ${formattedOrderDetail.tien_cod?.toLocaleString('vi-VN')}đ`);
    console.log(`   - tien_coc: ${formattedOrderDetail.tien_coc?.toLocaleString('vi-VN')}đ`);

    // 6. Verify API response structure
    console.log('\n6️⃣ Verifying API response structure...');
    
    const requiredFields = ['tien_cod', 'tien_coc'];
    const missingFields = [];
    
    requiredFields.forEach(field => {
      if (!(field in testOrder)) {
        missingFields.push(field);
      }
    });

    if (missingFields.length === 0) {
      console.log('✅ All required COD fields present in API response');
    } else {
      console.log(`❌ Missing fields: ${missingFields.join(', ')}`);
    }

    // 7. Test Frontend calculation
    console.log('\n7️⃣ Testing Frontend calculation logic...');
    
    // Simulate frontend logic
    const frontendCODCalc = {
      codAmount: testOrder.tien_cod || 0,
      paidAmount: testOrder.tong_da_tra || 0,
      remainingCOD: (testOrder.tien_cod || 0) - (testOrder.tong_da_tra || 0),
      canPay: (testOrder.tien_cod || 0) - (testOrder.tong_da_tra || 0) > 0,
      paymentStatus: (testOrder.tien_cod || 0) - (testOrder.tong_da_tra || 0) <= 0 ? 'Đã thanh toán đủ COD' : `Còn COD: ${((testOrder.tien_cod || 0) - (testOrder.tong_da_tra || 0)).toLocaleString('vi-VN')}đ`
    };

    console.log('🖥️ Frontend Calculation:');
    console.log(`   - COD Amount: ${frontendCODCalc.codAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - Paid Amount: ${frontendCODCalc.paidAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - Remaining COD: ${frontendCODCalc.remainingCOD.toLocaleString('vi-VN')}đ`);
    console.log(`   - Can Pay: ${frontendCODCalc.canPay}`);
    console.log(`   - Payment Status: ${frontendCODCalc.paymentStatus}`);

    // 8. Cleanup
    console.log('\n8️⃣ Cleaning up...');
    await order.destroy();
    await customer.destroy();
    console.log('✅ Cleanup completed');

    // 9. Final verification
    console.log('\n9️⃣ Final Verification:');
    const expectedCOD = 500000;
    const expectedRemaining = 300000;
    
    if (testOrder.tien_cod === expectedCOD && frontendCODCalc.remainingCOD === expectedRemaining) {
      console.log('🎉 All tests passed! API now returns tien_cod correctly.');
      console.log('✅ Frontend can now display COD column properly.');
    } else {
      console.log('❌ Tests failed!');
      console.log(`   Expected COD: ${expectedCOD}, Got: ${testOrder.tien_cod}`);
      console.log(`   Expected Remaining: ${expectedRemaining}, Got: ${frontendCODCalc.remainingCOD}`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await sequelize.close();
  }
}

// Chạy test
testOrdersAPICOD();
