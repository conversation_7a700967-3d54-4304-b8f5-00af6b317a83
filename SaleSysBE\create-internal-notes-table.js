const { sequelize } = require('./models');

async function createInternalNotesTable() {
  try {
    console.log('🔧 Creating internal_notes table...');

    // Tạo bảng internal_notes
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS internal_notes (
        id INT PRIMARY KEY AUTO_INCREMENT,
        customer_id INT NOT NULL,
        content TEXT NOT NULL,
        type ENUM('reminder', 'contact', 'payment', 'general') NOT NULL DEFAULT 'general',
        created_by VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_customer_id (customer_id),
        INDEX idx_created_at (created_at),
        FOREIGN KEY (customer_id) REFERENCES nguoi_dung(id) ON DELETE CASCADE ON UPDATE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    console.log('✅ Successfully created internal_notes table');
    
    // Kiểm tra bảng đã được tạo
    const [results] = await sequelize.query(`
      SHOW TABLES LIKE 'internal_notes'
    `);
    
    if (results.length > 0) {
      console.log('📋 Table internal_notes exists');
      
      // Hiển thị cấu trúc bảng
      const [structure] = await sequelize.query(`
        DESCRIBE internal_notes
      `);
      
      console.log('📋 Table structure:');
      console.table(structure);
      
      // Thêm một số dữ liệu mẫu
      console.log('📝 Adding sample data...');
      
      await sequelize.query(`
        INSERT INTO internal_notes (customer_id, content, type, created_by) VALUES
        (14, 'Khách hàng đã hẹn thanh toán vào cuối tháng', 'reminder', 'Admin'),
        (14, 'Đã gọi điện nhắc nhở, khách hàng cam kết thanh toán trong tuần', 'contact', 'Admin'),
        (20, 'Khách VIP, ưu tiên xử lý', 'general', 'Admin'),
        (20, 'Đã thanh toán đúng hạn, khách hàng tin cậy', 'payment', 'Admin')
        ON DUPLICATE KEY UPDATE content = VALUES(content);
      `);
      
      console.log('✅ Sample data added successfully');
      
      // Kiểm tra dữ liệu
      const [sampleData] = await sequelize.query(`
        SELECT * FROM internal_notes ORDER BY created_at DESC LIMIT 5
      `);
      
      console.log('📋 Sample data in table:');
      console.table(sampleData);
      
    } else {
      console.log('❌ Table internal_notes was not created');
    }

  } catch (error) {
    console.error('❌ Error creating internal_notes table:', error.message);
    console.error('Full error:', error);
  } finally {
    await sequelize.close();
  }
}

// Chạy script
createInternalNotesTable();
