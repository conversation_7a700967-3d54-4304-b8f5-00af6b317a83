import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Card,
  Table,
  Input,
  Select,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Tag,
  DatePicker,
  Modal,
  Form,
  InputNumber,
  message,
  Tooltip,
  Divider,
  Upload,
  Checkbox,
  Radio,
  Breadcrumb,
} from "antd";
import {
  SearchOutlined,
  FilterOutlined,
  ExportOutlined,
  EyeOutlined,
  PlusOutlined,
  DollarOutlined,
  UserOutlined,
  CalendarOutlined,
  FileTextOutlined,
  UploadOutlined,
  PaperClipOutlined,
} from "@ant-design/icons";
import { useQuery, useMutation, useQueryClient } from "react-query";
import dayjs from "dayjs";
import { testAPI } from "../../services/api";

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

// Hook để lấy dữ liệu công nợ
const useDebtData = (filters) => {
  return useQuery(
    ["debt-management", filters],
    async () => {
      const queryParams = new URLSearchParams();

      if (filters.search) queryParams.append("search", filters.search);
      if (filters.status) queryParams.append("status", filters.status);
      if (filters.dateRange?.[0])
        queryParams.append("start_date", filters.dateRange[0]);
      if (filters.dateRange?.[1])
        queryParams.append("end_date", filters.dateRange[1]);
      if (filters.page) queryParams.append("page", filters.page);
      if (filters.limit) queryParams.append("limit", filters.limit);

      const response = await testAPI.getDebtList(filters);
      return response;
    },
    {
      keepPreviousData: true,
      refetchOnWindowFocus: false,
    }
  );
};

// Hook để tạo phiếu thu/chi
const useCreatePayment = () => {
  const queryClient = useQueryClient();

  return useMutation(
    async (paymentData) => {
      console.log("🌐 Calling API with data:", paymentData);

      try {
        console.log("🔗 Making payment request");
        console.log(
          "📦 Request payload:",
          JSON.stringify(paymentData, null, 2)
        );

        // Use testAPI for authenticated request
        const result = await testAPI.createPayment(paymentData);
        console.log("✅ API Success:", result);
        return result;
      } catch (error) {
        console.error("🔥 Network Error:", error);

        // Nếu server không chạy, trả về mock response
        if (error.message.includes("fetch")) {
          console.log("🔄 Server not available, using mock response");
          return {
            success: true,
            message: "Thu tiền thành công (Mock)",
            data: {
              payment: {
                id: Date.now(),
                ...paymentData,
                created_at: new Date().toISOString(),
              },
            },
          };
        }

        throw error;
      }
    },
    {
      onSuccess: () => {
        message.success("Tạo phiếu thu/chi thành công!");
        queryClient.invalidateQueries(["debt-management"]);
      },
      onError: (error) => {
        message.error(`Lỗi: ${error.message}`);
      },
    }
  );
};

const DebtManagement = () => {
  const navigate = useNavigate();
  const [filters, setFilters] = useState({
    search: "",
    status: "",
    customerGroup: "",
    debtRange: "",
    overdueOnly: false,
    dateRange: null,
    page: 1,
    limit: 20,
  });

  const [savedFilters, setSavedFilters] = useState([
    {
      name: "Khách nợ quá hạn > 60 ngày",
      filters: { overdueOnly: true, debtRange: "high" },
    },
    {
      name: "VIP có công nợ",
      filters: { customerGroup: "vip", status: "warning" },
    },
    {
      name: "Đại lý nợ cao",
      filters: { customerGroup: "agency", debtRange: "high" },
    },
  ]);

  const [customerGroups, setCustomerGroups] = useState([]); // Thêm state cho nhóm khách hàng
  const [advancedFilterVisible, setAdvancedFilterVisible] = useState(false);

  const [paymentModal, setPaymentModal] = useState({
    visible: false,
    customer: null,
    type: "thu", // 'thu' hoặc 'chi'
  });

  const [form] = Form.useForm();
  const { data, isLoading, error } = useDebtData(filters);
  const createPaymentMutation = useCreatePayment();

  // Cấu hình cột cho bảng công nợ
  const columns = [
    {
      title: "STT",
      key: "index",
      width: 60,
      render: (_, __, index) => (filters.page - 1) * filters.limit + index + 1,
    },
    {
      title: "Khách hàng",
      dataIndex: "customer_name",
      key: "customer_name",
      width: 200,
      render: (text, record) => (
        <Space direction="vertical" size="small">
          <Button
            type="link"
            onClick={() => handleViewDetail(record)}
            style={{ padding: 0, height: "auto", fontWeight: "bold" }}
          >
            {text}
          </Button>
          <Text type="secondary" style={{ fontSize: "12px" }}>
            📞 {record.customer_phone}
          </Text>
        </Space>
      ),
    },
    {
      title: "Công nợ hiện tại",
      dataIndex: "total_debt",
      key: "total_debt",
      width: 150,
      align: "right",
      render: (value) => {
        const isNegative = value < 0;
        const isZero = value === 0;

        let color = "#52c41a"; // Xanh cho số 0 hoặc âm
        let prefix = "";

        if (value > 0) {
          color = "#ff4d4f"; // Đỏ cho nợ dương
        } else if (isNegative) {
          color = "#1890ff"; // Xanh dương cho số âm (khách hàng thừa tiền)
          prefix = "-";
        }

        return (
          <div style={{ textAlign: "right" }}>
            <Text strong style={{ color }}>
              {prefix}
              {new Intl.NumberFormat("vi-VN", {
                style: "currency",
                currency: "VND",
              }).format(Math.abs(value))}
            </Text>
            <div style={{ fontSize: "11px", color: "#666" }}>(Dự tính)</div>
          </div>
        );
      },
      sorter: (a, b) => a.total_debt - b.total_debt,
    },
    {
      title: "Công nợ thực tế",
      dataIndex: "actual_debt",
      key: "actual_debt",
      width: 150,
      align: "right",
      render: (value) => {
        const isZero = (value || 0) === 0;

        return (
          <div style={{ textAlign: "right" }}>
            <Text
              strong
              style={{
                color: isZero ? "#52c41a" : "#fa8c16",
                fontSize: "14px",
              }}
            >
              {new Intl.NumberFormat("vi-VN", {
                style: "currency",
                currency: "VND",
              }).format(value || 0)}
            </Text>
            <div style={{ fontSize: "11px", color: "#666" }}>(Đã giao)</div>
          </div>
        );
      },
      sorter: (a, b) => (a.actual_debt || 0) - (b.actual_debt || 0),
    },
    {
      title: "Nợ quá hạn",
      dataIndex: "overdue_debt",
      key: "overdue_debt",
      width: 150,
      align: "right",
      render: (value) => (
        <Text style={{ color: value > 0 ? "#ff4d4f" : "#666" }}>
          {new Intl.NumberFormat("vi-VN", {
            style: "currency",
            currency: "VND",
          }).format(value)}
        </Text>
      ),
    },
    {
      title: "Số đơn hàng",
      dataIndex: "order_count",
      key: "order_count",
      width: 100,
      align: "center",
    },
    {
      title: "Ngày cập nhật",
      dataIndex: "last_updated",
      key: "last_updated",
      width: 120,
      render: (date) => dayjs(date).format("DD/MM/YYYY"),
    },
    {
      title: "Nhóm KH",
      dataIndex: "customer_group",
      key: "customer_group",
      width: 120,
      render: (group, record) => {
        if (!group || group === "Chưa phân nhóm") {
          return <Tag color="default">Chưa phân nhóm</Tag>;
        }

        // Mapping màu sắc dựa trên mã nhóm
        const getGroupColor = (groupCode) => {
          const colors = {
            VIP: "gold",
            LOYAL: "orange",
            REGULAR: "green",
            NEW: "blue",
            BUSINESS: "purple",
            RETAIL: "cyan",
            BANLE: "green",
            BANSI: "orange",
            test: "default",
            test1: "magenta",
            test2: "lime",
          };
          return colors[groupCode] || "default";
        };

        const color = getGroupColor(record.customer_group_code);

        return (
          <Tag color={color} title={group}>
            {record.customer_group_code || group}
          </Tag>
        );
      },
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      width: 120,
      render: (status, record) => {
        let statusConfig;

        if (record.total_debt < 0) {
          // Khách hàng thừa tiền (đã trả nhiều hơn nợ)
          statusConfig = {
            color: "cyan",
            text: "Thừa tiền",
            icon: "💰",
            tooltip: `Khách hàng đã trả thừa ${new Intl.NumberFormat("vi-VN", {
              style: "currency",
              currency: "VND",
            }).format(Math.abs(record.total_debt))}`,
          };
        } else if (record.total_debt === 0) {
          statusConfig = { color: "success", text: "Đã thu đủ", icon: "✓" };
        } else if (record.overdue_debt > 0) {
          statusConfig = { color: "error", text: "Quá hạn", icon: "⚠" };
        } else if (record.total_debt > 5000000) {
          statusConfig = { color: "warning", text: "Cảnh báo", icon: "!" };
        } else {
          statusConfig = { color: "processing", text: "Còn nợ", icon: "○" };
        }

        return (
          <Tooltip title={statusConfig.tooltip || statusConfig.text}>
            <Tag color={statusConfig.color} style={{ fontWeight: "bold" }}>
              {statusConfig.icon} {statusConfig.text}
            </Tag>
          </Tooltip>
        );
      },
    },
    {
      title: "Thao tác",
      key: "actions",
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="Xem chi tiết công nợ">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
              style={{ color: "#1890ff" }}
            />
          </Tooltip>

          {/* Button thu tiền - chỉ hiện khi có nợ dương */}
          {record.total_debt > 0 && (
            <Tooltip title="Tạo phiếu thu tiền">
              <Button
                type="text"
                icon={<DollarOutlined />}
                onClick={() => handleCreatePayment(record, "thu")}
                style={{ color: "#52c41a" }}
              />
            </Tooltip>
          )}

          {/* Button hoàn tiền - chỉ hiện khi khách hàng thừa tiền */}
          {record.total_debt < 0 && (
            <Tooltip title="Tạo phiếu hoàn tiền">
              <Button
                type="text"
                icon={<PlusOutlined />}
                onClick={() => handleCreatePayment(record, "chi")}
                style={{ color: "#ff4d4f" }}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  // Xử lý xem chi tiết
  const handleViewDetail = (customer) => {
    console.log("View detail for customer:", customer);
    if (!customer.customer_id) {
      message.error("Không tìm thấy ID khách hàng");
      return;
    }

    message.loading("Đang chuyển đến trang chi tiết...", 0.5);
    navigate(`/accounting/debt/${customer.customer_id}`);
  };

  // Xử lý tạo phiếu thu/chi
  const handleCreatePayment = (customer, type) => {
    setPaymentModal({
      visible: true,
      customer,
      type,
    });
    form.resetFields();

    // Tính toán số tiền mặc định
    let defaultAmount = 0;
    if (type === "thu" && customer.total_debt > 0) {
      defaultAmount = customer.total_debt;
    } else if (type === "chi" && customer.total_debt < 0) {
      defaultAmount = Math.abs(customer.total_debt); // Số tiền hoàn lại
    }

    form.setFieldsValue({
      customer_id: customer.customer_id,
      customer_name: customer.customer_name,
      type,
      amount: defaultAmount,
      payment_method: "cash", // Default value
      is_partial_payment: false,
    });
  };

  // Xử lý submit phiếu thu/chi
  const handlePaymentSubmit = async (values) => {
    try {
      console.log("🚀 Submitting payment:", values);
      console.log("🎯 Customer:", paymentModal.customer);

      const paymentData = {
        ...values,
        customer_id: paymentModal.customer.customer_id,
        type: paymentModal.type,
      };

      console.log("📤 Sending payment data:", paymentData);

      const result = await createPaymentMutation.mutateAsync(paymentData);

      console.log("✅ Payment result:", result);

      // Hiển thị thông báo thành công với thông tin chi tiết
      const amountFormatted = new Intl.NumberFormat("vi-VN", {
        style: "currency",
        currency: "VND",
      }).format(paymentData.amount);

      message.success(
        `Thu tiền thành công! Đã thu ${amountFormatted} từ ${paymentModal.customer.customer_name}. Công nợ đã được cập nhật.`,
        5 // Hiển thị 5 giây
      );

      setPaymentModal({ visible: false, customer: null, type: "thu" });
      form.resetFields();

      // Refresh data để cập nhật bảng (đã tự động invalidate trong onSuccess của mutation)
    } catch (error) {
      console.error("❌ Error creating payment:", error);
      message.error(error.message || "Có lỗi xảy ra khi tạo phiếu thu/chi");
    }
  };

  // Xử lý thay đổi bộ lọc
  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
      page: 1, // Reset về trang đầu khi filter
    }));
  };

  // Xử lý thay đổi trang
  const handleTableChange = (pagination) => {
    setFilters((prev) => ({
      ...prev,
      page: pagination.current,
      limit: pagination.pageSize,
    }));
  };

  if (error) {
    return (
      <Card>
        <div style={{ textAlign: "center", padding: "50px" }}>
          <Text type="danger">Lỗi: {error.message}</Text>
        </div>
      </Card>
    );
  }

  const debtData = data?.data || [];
  const stats = data?.stats || {};
  const pagination = data?.pagination || {};

  return (
    <div style={{ padding: "24px" }}>
      {/* Breadcrumb */}
      <Breadcrumb style={{ marginBottom: "16px" }}>
        <Breadcrumb.Item>Kế toán</Breadcrumb.Item>
        <Breadcrumb.Item>Quản lý công nợ</Breadcrumb.Item>
      </Breadcrumb>

      {/* Header */}
      <div style={{ marginBottom: "24px" }}>
        <Title level={2}>
          <UserOutlined style={{ marginRight: "8px" }} />
          Quản lý công nợ khách hàng
        </Title>
        <Text type="secondary">
          Theo dõi và quản lý công nợ của khách hàng. Click vào tên khách hàng
          hoặc biểu tượng mắt để xem chi tiết.
        </Text>

        {/* Debug & Sync Buttons */}
        <div style={{ marginTop: "16px" }}>
          <Space>
            <Button
              type="primary"
              onClick={async () => {
                console.log("🔄 Syncing debt from orders...");
                try {
                  // Sử dụng testAPI để có authentication tự động
                  const response = await testAPI.syncDebtFromOrders();
                  console.log("🔄 Sync result:", response);

                  if (response.success) {
                    message.success(
                      `Đồng bộ công nợ thành công! Đã cập nhật ${
                        response.data?.synced_customers || 0
                      } khách hàng.`
                    );
                    // Refresh data
                    window.location.reload();
                  } else {
                    message.error(response.message || "Lỗi đồng bộ công nợ!");
                  }
                } catch (error) {
                  console.error("🔄 Sync error:", error);
                  message.error(`Lỗi đồng bộ công nợ: ${error.message}`);
                }
              }}
            >
              🔄 Đồng bộ công nợ từ đơn hàng
            </Button>

            <Button
              type="dashed"
              onClick={async () => {
                console.log("🔐 Checking authentication...");
                const token = localStorage.getItem("token");
                console.log("Token exists:", !!token);
                console.log(
                  "Token preview:",
                  token ? token.substring(0, 20) + "..." : "No token"
                );

                try {
                  // Test với API có auth
                  const response = await testAPI.getDebtList({ limit: 1 });
                  console.log("✅ Auth test successful:", response);
                  message.success("Authentication OK! Token hợp lệ.");
                } catch (error) {
                  console.error("❌ Auth test failed:", error);
                  message.error(`Authentication failed: ${error.message}`);
                }
              }}
            >
              🔐 Test Authentication
            </Button>
          </Space>
        </div>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Công nợ hiện tại"
              value={stats.total_debt || 0}
              formatter={(value) =>
                new Intl.NumberFormat("vi-VN", {
                  style: "currency",
                  currency: "VND",
                }).format(value)
              }
              valueStyle={{ color: "#ff4d4f" }}
              suffix={
                <div
                  style={{ fontSize: "12px", color: "#666", marginTop: "4px" }}
                >
                  (Dự tính)
                </div>
              }
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Công nợ thực tế"
              value={stats.actual_debt || 0}
              formatter={(value) =>
                new Intl.NumberFormat("vi-VN", {
                  style: "currency",
                  currency: "VND",
                }).format(value)
              }
              valueStyle={{ color: "#fa8c16" }}
              suffix={
                <div
                  style={{ fontSize: "12px", color: "#666", marginTop: "4px" }}
                >
                  (Đã giao)
                </div>
              }
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Nợ quá hạn"
              value={stats.overdue_debt || 0}
              formatter={(value) =>
                new Intl.NumberFormat("vi-VN", {
                  style: "currency",
                  currency: "VND",
                }).format(value)
              }
              valueStyle={{ color: "#ff7875" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="KH có nợ thực tế"
              value={stats.customers_with_actual_debt || 0}
              valueStyle={{ color: "#1890ff" }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: "24px" }}>
        <Row gutter={[16, 16]} align="middle">
          <Col span={6}>
            <Input
              placeholder="Tìm kiếm khách hàng..."
              prefix={<SearchOutlined />}
              value={filters.search}
              onChange={(e) => handleFilterChange("search", e.target.value)}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Trạng thái"
              value={filters.status}
              onChange={(value) => handleFilterChange("status", value)}
              allowClear
              style={{ width: "100%" }}
            >
              <Option value="normal">Bình thường</Option>
              <Option value="warning">Cảnh báo</Option>
              <Option value="overdue">Quá hạn</Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              placeholder={["Từ ngày", "Đến ngày"]}
              value={filters.dateRange}
              onChange={(dates) => handleFilterChange("dateRange", dates)}
              format="DD/MM/YYYY"
              style={{ width: "100%" }}
            />
          </Col>
          <Col span={8}>
            <Space>
              <Button
                icon={<FilterOutlined />}
                onClick={() => setAdvancedFilterVisible(!advancedFilterVisible)}
              >
                Bộ lọc nâng cao
              </Button>
              <Button icon={<ExportOutlined />}>Xuất Excel</Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Advanced Filters */}
      {advancedFilterVisible && (
        <Card title="Bộ lọc nâng cao" style={{ marginBottom: "24px" }}>
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Space
                direction="vertical"
                size="small"
                style={{ width: "100%" }}
              >
                <Text strong>Nhóm khách hàng</Text>
                <Select
                  placeholder="Chọn nhóm"
                  value={filters.customerGroup}
                  onChange={(value) =>
                    handleFilterChange("customerGroup", value)
                  }
                  allowClear
                  style={{ width: "100%" }}
                >
                  <Option value="vip">VIP</Option>
                  <Option value="agency">Đại lý</Option>
                  <Option value="wholesale">Khách sỉ</Option>
                  <Option value="retail">Khách lẻ</Option>
                </Select>
              </Space>
            </Col>
            <Col span={6}>
              <Space
                direction="vertical"
                size="small"
                style={{ width: "100%" }}
              >
                <Text strong>Mức công nợ</Text>
                <Select
                  placeholder="Chọn mức nợ"
                  value={filters.debtRange}
                  onChange={(value) => handleFilterChange("debtRange", value)}
                  allowClear
                  style={{ width: "100%" }}
                >
                  <Option value="low">Dưới 1 triệu</Option>
                  <Option value="medium">1-5 triệu</Option>
                  <Option value="high">Trên 5 triệu</Option>
                  <Option value="very_high">Trên 10 triệu</Option>
                </Select>
              </Space>
            </Col>
            <Col span={6}>
              <Space
                direction="vertical"
                size="small"
                style={{ width: "100%" }}
              >
                <Text strong>Bộ lọc nhanh</Text>
                <Select
                  placeholder="Chọn bộ lọc đã lưu"
                  onChange={(value) => {
                    const savedFilter = savedFilters.find(
                      (f) => f.name === value
                    );
                    if (savedFilter) {
                      setFilters((prev) => ({
                        ...prev,
                        ...savedFilter.filters,
                      }));
                    }
                  }}
                  style={{ width: "100%" }}
                >
                  {savedFilters.map((filter) => (
                    <Option key={filter.name} value={filter.name}>
                      {filter.name}
                    </Option>
                  ))}
                </Select>
              </Space>
            </Col>
            <Col span={6}>
              <Space
                direction="vertical"
                size="small"
                style={{ width: "100%" }}
              >
                <Text strong>Tùy chọn khác</Text>
                <Space direction="vertical">
                  <label>
                    <input
                      type="checkbox"
                      checked={filters.overdueOnly}
                      onChange={(e) =>
                        handleFilterChange("overdueOnly", e.target.checked)
                      }
                      style={{ marginRight: "8px" }}
                    />
                    Chỉ hiển thị nợ quá hạn
                  </label>
                </Space>
              </Space>
            </Col>
          </Row>
        </Card>
      )}

      {/* Main Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={debtData}
          rowKey="customer_id"
          loading={isLoading}
          pagination={{
            current: pagination.page || 1,
            pageSize: pagination.limit || 20,
            total: pagination.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} khách hàng`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Payment Modal */}
      <Modal
        title={`Tạo phiếu ${
          paymentModal.type === "thu" ? "thu tiền" : "hoàn tiền"
        }`}
        open={paymentModal.visible}
        onCancel={() =>
          setPaymentModal({ visible: false, customer: null, type: "thu" })
        }
        footer={null}
        width={700}
      >
        <Form form={form} layout="vertical" onFinish={handlePaymentSubmit}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Khách hàng" name="customer_name">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Loại phiếu" name="type">
                <Select disabled>
                  <Option value="thu">Phiếu thu</Option>
                  <Option value="chi">Phiếu chi</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={(() => {
                  const currentDebt = paymentModal.customer?.total_debt || 0;
                  const actualDebt = paymentModal.customer?.actual_debt || 0;

                  const formattedCurrent = new Intl.NumberFormat("vi-VN", {
                    style: "currency",
                    currency: "VND",
                  }).format(Math.abs(currentDebt));

                  const formattedActual = new Intl.NumberFormat("vi-VN", {
                    style: "currency",
                    currency: "VND",
                  }).format(Math.abs(actualDebt));

                  if (currentDebt > 0) {
                    return (
                      <div>
                        Số tiền thu
                        <div
                          style={{
                            fontSize: "12px",
                            color: "#666",
                            fontWeight: "normal",
                          }}
                        >
                          Công nợ hiện tại: {formattedCurrent} | Thực tế:{" "}
                          {formattedActual}
                        </div>
                      </div>
                    );
                  } else if (currentDebt < 0) {
                    return `Số tiền thừa: ${formattedCurrent}`;
                  } else {
                    return `Không có công nợ`;
                  }
                })()}
                name="amount"
                rules={[
                  { required: true, message: "Vui lòng nhập số tiền!" },
                  {
                    type: "number",
                    min: 1,
                    message: "Số tiền phải lớn hơn 0!",
                  },
                  {
                    validator: (_, value) => {
                      const debt = paymentModal.customer?.total_debt || 0;

                      if (paymentModal.type === "thu" && debt > 0) {
                        // Trường hợp thu tiền: không được vượt quá tổng nợ
                        if (value && value > debt) {
                          return Promise.reject(
                            new Error("Số tiền không được vượt quá tổng nợ!")
                          );
                        }
                      } else if (paymentModal.type === "chi" && debt < 0) {
                        // Trường hợp hoàn tiền: không được vượt quá số tiền thừa
                        if (value && value > Math.abs(debt)) {
                          return Promise.reject(
                            new Error(
                              "Số tiền không được vượt quá số tiền thừa!"
                            )
                          );
                        }
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  formatter={(value) =>
                    `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                  }
                  parser={(value) => value.replace(/\$\s?|(,*)/g, "")}
                  placeholder={
                    paymentModal.type === "thu"
                      ? "Nhập số tiền thu"
                      : "Nhập số tiền hoàn"
                  }
                  max={(() => {
                    const debt = paymentModal.customer?.total_debt || 0;
                    return paymentModal.type === "thu" ? debt : Math.abs(debt);
                  })()}
                  min={1}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Phương thức thanh toán (chỉ để ghi nhận)"
                name="payment_method"
                initialValue="cash"
              >
                <Select placeholder="Chọn phương thức thanh toán">
                  <Option value="cash">💵 Tiền mặt</Option>
                  <Option value="transfer">🏦 Chuyển khoản</Option>
                  <Option value="card">💳 Thẻ tín dụng/ghi nợ</Option>
                  <Option value="check">📝 Séc</Option>
                  <Option value="other">🔄 Khác</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="Đính kèm chứng từ" name="attachments">
            <Upload
              multiple
              beforeUpload={() => false}
              listType="text"
              maxCount={5}
            >
              <Button icon={<UploadOutlined />}>
                Tải lên hóa đơn/ảnh chuyển khoản
              </Button>
            </Upload>
            <Text type="secondary" style={{ fontSize: "12px" }}>
              Hỗ trợ: JPG, PNG, PDF. Tối đa 5 file.
            </Text>
          </Form.Item>

          <Form.Item label="Ghi chú" name="note">
            <TextArea rows={3} placeholder="Ghi chú về phiếu thu/chi..." />
          </Form.Item>

          <Form.Item name="is_partial_payment" valuePropName="checked">
            <Checkbox>
              Đây là thanh toán từng phần (không đóng hoàn toàn công nợ)
            </Checkbox>
          </Form.Item>

          <Divider />

          <div
            style={{
              backgroundColor: "#f6ffed",
              border: "1px solid #b7eb8f",
              borderRadius: "6px",
              padding: "12px",
              marginBottom: "16px",
            }}
          >
            <Text strong style={{ color: "#52c41a" }}>
              💡 Lưu ý:
            </Text>
            <br />
            <Text type="secondary">
              • Phương thức thanh toán chỉ để ghi nhận, không ảnh hưởng đến xử
              lý
              <br />
              {paymentModal.type === "thu" ? (
                <>
                  • Số tiền thu sẽ được{" "}
                  <strong>trừ trực tiếp vào công nợ</strong> của khách hàng
                  <br />• Công nợ còn lại:{" "}
                  {new Intl.NumberFormat("vi-VN", {
                    style: "currency",
                    currency: "VND",
                  }).format(
                    Math.max(
                      0,
                      (paymentModal.customer?.total_debt || 0) -
                        (form.getFieldValue("amount") || 0)
                    )
                  )}
                </>
              ) : (
                <>
                  • Số tiền hoàn sẽ được <strong>trả lại cho khách hàng</strong>
                  <br />• Số tiền thừa còn lại:{" "}
                  {new Intl.NumberFormat("vi-VN", {
                    style: "currency",
                    currency: "VND",
                  }).format(
                    Math.max(
                      0,
                      Math.abs(paymentModal.customer?.total_debt || 0) -
                        (form.getFieldValue("amount") || 0)
                    )
                  )}
                </>
              )}
            </Text>
          </div>

          <Form.Item>
            <Space style={{ width: "100%", justifyContent: "flex-end" }}>
              <Button
                onClick={() =>
                  setPaymentModal({
                    visible: false,
                    customer: null,
                    type: "thu",
                  })
                }
              >
                Hủy
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createPaymentMutation.isLoading}
                icon={<DollarOutlined />}
              >
                {paymentModal.type === "thu"
                  ? "Thu tiền và cập nhật công nợ"
                  : "Hoàn tiền cho khách hàng"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DebtManagement;
