const {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>gNo<PERSON>,
  <PERSON><PERSON>,
  NhomKhachHang,
  NhomKhachHangNguoiDung,
  sequelize,
} = require("../models");
const { Op } = require("sequelize");

// L<PERSON>y danh sách công nợ khách hàng
const getDebtList = async (req, res) => {
  try {
    const {
      search = "",
      status = "",
      start_date,
      end_date,
      page = 1,
      limit = 20,
    } = req.query;

    console.log("📊 Getting debt list with filters:", req.query);

    // Xây dựng điều kiện where
    const whereConditions = {};

    if (search) {
      whereConditions[Op.or] = [
        { ho_ten: { [Op.like]: `%${search}%` } },
        { so_dien_thoai: { [Op.like]: `%${search}%` } },
      ];
    }

    // L<PERSON>y danh sách khách hàng có công nợ

    const customers = await <PERSON><PERSON><PERSON><PERSON><PERSON>.findAll({
      where: whereConditions,
      include: [
        {
          model: <PERSON><PERSON><PERSON>,
          as: "donH<PERSON><PERSON>hachHang",
          attributes: [
            "id",
            "tong_tien", // Tổng tiền sản phẩm
            "tong_phai_tra", // Tiền COD
            "tong_da_tra", // Đã thanh toán
            "con_phai_tra", // Công nợ
            "tien_cod", // Tiền COD
            "tien_coc", // Tiền cọc
            "ngay_ban",
            "trang_thai",
          ],
          required: false, // Lấy cả khách hàng chưa có đơn hàng
          where:
            start_date || end_date
              ? {
                  ...(start_date && { ngay_ban: { [Op.gte]: start_date } }),
                  ...(end_date && { ngay_ban: { [Op.lte]: end_date } }),
                }
              : undefined,
        },
        {
          model: NhomKhachHang,
          as: "nhomKhachHangList",
          through: { attributes: [] },
          required: false,
          attributes: ["id", "ten_nhom", "ma_nhom"],
        },
      ],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit),
    });

    // Đếm tổng số khách hàng (đơn giản hóa)
    const totalCount = customers.length;

    // Tính toán công nợ cho từng khách hàng
    const debtData = customers.map((customer) => {
      const orders = customer.donHangKhachHang || [];

      // Định nghĩa trạng thái đơn hàng
      const confirmedStatuses = [
        "da_xac_nhan",
        "da_dong_goi",
        "da_giao",
        "hoan_thanh",
      ];
      const completedStatuses = ["hoan_thanh"]; // Chỉ đơn hàng đã giao thành công
      const returnedStatuses = ["hoan_hang"]; // Đơn hàng hoàn

      // 1. Công nợ hiện tại (dự tính) - từ đơn hàng đã xác nhận trở lên
      const currentDebt = orders.reduce((sum, order) => {
        // Đơn hàng hoàn = công nợ 0
        if (returnedStatuses.includes(order.trang_thai)) {
          return sum;
        }
        // Chỉ tính đơn hàng đã xác nhận trở lên
        if (confirmedStatuses.includes(order.trang_thai)) {
          return sum + (parseFloat(order.con_phai_tra) || 0);
        }
        return sum;
      }, 0);

      // 2. Công nợ thực tế - chỉ từ đơn hàng đã giao thành công
      const actualDebt = orders.reduce((sum, order) => {
        // Đơn hàng hoàn = công nợ 0
        if (returnedStatuses.includes(order.trang_thai)) {
          return sum;
        }
        // Chỉ tính đơn hàng đã giao thành công
        if (completedStatuses.includes(order.trang_thai)) {
          return sum + (parseFloat(order.con_phai_tra) || 0);
        }
        return sum;
      }, 0);

      // Sử dụng currentDebt làm totalDebt cho backward compatibility
      const totalDebt = currentDebt;

      // Tính công nợ quá hạn từ đơn hàng (sử dụng con_phai_tra theo logic mới)
      const overdueDebt = orders.reduce((sum, order) => {
        const daysDiff = Math.floor(
          (new Date() - new Date(order.ngay_ban)) / (1000 * 60 * 60 * 24)
        );
        if (daysDiff > 30 && (order.con_phai_tra || 0) > 0) {
          return sum + (order.con_phai_tra || 0);
        }
        return sum;
      }, 0);

      // Xác định trạng thái
      let customerStatus = "normal";
      if (overdueDebt > 0) {
        customerStatus = "overdue";
      } else if (totalDebt > 5000000) {
        // Cảnh báo nếu nợ > 5 triệu
        customerStatus = "warning";
      }

      // Lấy nhóm khách hàng thực tế từ database
      const customerGroups = customer.nhomKhachHangList || [];
      let customerGroup = null;
      let customerGroupCode = null;

      if (customerGroups.length > 0) {
        // Ưu tiên nhóm đầu tiên, có thể customize logic này
        const primaryGroup = customerGroups[0];
        customerGroup = primaryGroup.ten_nhom;
        customerGroupCode = primaryGroup.ma_nhom;
      } else {
        // Fallback nếu không có nhóm
        customerGroup = "Chưa phân nhóm";
        customerGroupCode = "NONE";
      }

      const totalPurchased = orders.reduce(
        (sum, order) => sum + (order.tong_tien || 0),
        0
      );

      return {
        customer_id: customer.id,
        customer_name: customer.ho_ten,
        customer_phone: customer.so_dien_thoai,
        customer_email: customer.email,
        customer_group: customerGroup,
        customer_group_code: customerGroupCode,
        total_debt: totalDebt, // Công nợ hiện tại (dự tính)
        actual_debt: actualDebt, // Công nợ thực tế (đã giao thành công)
        overdue_debt: overdueDebt,
        order_count: orders.length,
        last_updated:
          orders.length > 0
            ? Math.max(...orders.map((o) => new Date(o.ngay_ban)))
            : customer.createdAt,
        status: customerStatus,
        total_purchased: totalPurchased,
      };
    });

    // Lọc theo trạng thái nếu có
    const filteredData = status
      ? debtData.filter((item) => item.status === status)
      : debtData;

    // Nếu không có dữ liệu thật, tạo dữ liệu mẫu để test
    if (filteredData.length === 0) {
      const mockData = [
        {
          customer_id: 1,
          customer_name: "Nguyễn Văn A",
          customer_phone: "0987654321",
          customer_email: "<EMAIL>",
          customer_group: "vip",
          total_debt: 15000000,
          overdue_debt: 5000000,
          order_count: 8,
          last_updated: new Date(),
          status: "overdue",
          total_purchased: 50000000,
        },
        {
          customer_id: 2,
          customer_name: "Trần Thị B",
          customer_phone: "0976543210",
          customer_email: "<EMAIL>",
          customer_group: "agency",
          total_debt: 8000000,
          overdue_debt: 0,
          order_count: 5,
          last_updated: new Date(),
          status: "warning",
          total_purchased: 25000000,
        },
        {
          customer_id: 3,
          customer_name: "Lê Văn C",
          customer_phone: "0965432109",
          customer_email: "<EMAIL>",
          customer_group: "retail",
          total_debt: 2000000,
          overdue_debt: 0,
          order_count: 3,
          last_updated: new Date(),
          status: "normal",
          total_purchased: 8000000,
        },
      ];

      return res.json({
        success: true,
        data: mockData,
        stats: {
          total_debt: 25000000,
          overdue_debt: 5000000,
          customers_with_debt: 3,
          overdue_customers: 1,
        },
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: 3,
          pages: 1,
        },
      });
    }

    // Tính thống kê tổng quan
    const stats = {
      total_debt: debtData.reduce((sum, item) => sum + item.total_debt, 0), // Công nợ hiện tại (dự tính)
      actual_debt: debtData.reduce((sum, item) => sum + item.actual_debt, 0), // Công nợ thực tế
      overdue_debt: debtData.reduce((sum, item) => sum + item.overdue_debt, 0),
      customers_with_debt: debtData.filter((item) => item.total_debt > 0)
        .length,
      customers_with_actual_debt: debtData.filter(
        (item) => item.actual_debt > 0
      ).length,
      overdue_customers: debtData.filter((item) => item.overdue_debt > 0)
        .length,
    };

    console.log("✅ Debt list retrieved successfully");
    res.json({
      success: true,
      data: filteredData,
      stats,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit)),
      },
    });
  } catch (error) {
    console.error("❌ Error in getDebtList:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy danh sách công nợ",
      error: error.message,
    });
  }
};

// Lấy chi tiết công nợ khách hàng
const getCustomerDebtDetail = async (req, res) => {
  try {
    const { customerId } = req.params;
    console.log(`📊 Getting debt detail for customer: ${customerId}`);

    const { Payment } = require("../models");

    const customer = await NguoiDung.findByPk(customerId, {
      include: [
        {
          model: DonHang,
          as: "donHangKhachHang",
          include: [
            {
              model: DonHangSanPham,
              as: "sanPhamList",
              attributes: ["ten_san_pham", "so_luong", "don_gia"],
            },
          ],
        },
      ],
    });

    // Lấy lịch sử thanh toán từ bảng payments
    console.log(`🔍 Fetching payments for customer: ${customerId}`);
    let payments = [];
    try {
      payments = await Payment.findAll({
        where: { customer_id: customerId },
        order: [["created_at", "DESC"]],
      });
      console.log(
        `💰 Found ${payments.length} payments:`,
        payments.map((p) => ({ id: p.id, amount: p.amount, type: p.type }))
      );
    } catch (error) {
      console.log(
        `⚠️ Payments table not available, using empty array:`,
        error.message
      );
      payments = [];
    }

    if (!customer) {
      // Tạo dữ liệu mẫu nếu không tìm thấy khách hàng thật
      const mockCustomerData = {
        1: {
          customer_id: 1,
          customer_name: "Nguyễn Văn A",
          customer_phone: "0987654321",
          customer_email: "<EMAIL>",
          customer_address: "123 Đường ABC, Quận 1, TP.HCM",
          created_date: "2024-01-15",
          last_order_date: "2025-06-30",
          total_debt: 15000000,
          total_purchased: 50000000,
          orders: [
            {
              order_id: 1,
              order_code: "DH202507010001",
              order_date: "2025-07-01",
              total_amount: 10000000,
              paid_amount: 5000000,
              debt_amount: 5000000,
              payment_status: "partial",
            },
            {
              order_id: 2,
              order_code: "DH202506150002",
              order_date: "2025-06-15",
              total_amount: 15000000,
              paid_amount: 5000000,
              debt_amount: 10000000,
              payment_status: "partial",
            },
          ],
          payments: [
            {
              payment_id: 1,
              payment_date: "2025-07-02 10:30:00",
              amount: 5000000,
              payment_method: "Chuyển khoản",
              note: "Thanh toán một phần đơn hàng DH202507010001",
              created_by: "Nguyễn Văn Admin",
            },
          ],
          timeline: [
            {
              type: "order",
              title: "Đơn hàng DH202507010001",
              date: "2025-07-01",
              description: "Tổng tiền: 10,000,000 VND",
            },
            {
              type: "payment",
              title: "Thanh toán",
              date: "2025-07-02",
              description: "Thanh toán: 5,000,000 VND",
            },
          ],
        },
        2: {
          customer_id: 2,
          customer_name: "Trần Thị B",
          customer_phone: "0976543210",
          customer_email: "<EMAIL>",
          customer_address: "456 Đường XYZ, Quận 2, TP.HCM",
          created_date: "2024-03-20",
          last_order_date: "2025-06-25",
          total_debt: 8000000,
          total_purchased: 25000000,
          orders: [
            {
              order_id: 3,
              order_code: "DH202506250003",
              order_date: "2025-06-25",
              total_amount: 8000000,
              paid_amount: 0,
              debt_amount: 8000000,
              payment_status: "unpaid",
            },
          ],
          payments: [],
          timeline: [
            {
              type: "order",
              title: "Đơn hàng DH202506250003",
              date: "2025-06-25",
              description: "Tổng tiền: 8,000,000 VND",
            },
          ],
        },
        3: {
          customer_id: 3,
          customer_name: "Lê Văn C",
          customer_phone: "0965432109",
          customer_email: "<EMAIL>",
          customer_address: "789 Đường DEF, Quận 3, TP.HCM",
          created_date: "2024-05-10",
          last_order_date: "2025-06-20",
          total_debt: 2000000,
          total_purchased: 8000000,
          orders: [
            {
              order_id: 4,
              order_code: "DH202506200004",
              order_date: "2025-06-20",
              total_amount: 2000000,
              paid_amount: 0,
              debt_amount: 2000000,
              payment_status: "unpaid",
            },
          ],
          payments: [],
          timeline: [
            {
              type: "order",
              title: "Đơn hàng DH202506200004",
              date: "2025-06-20",
              description: "Tổng tiền: 2,000,000 VND",
            },
          ],
        },
      };

      const mockCustomer = mockCustomerData[customerId];
      if (!mockCustomer) {
        return res.status(404).json({
          success: false,
          message: "Không tìm thấy khách hàng",
        });
      }

      console.log("✅ Mock customer debt detail retrieved successfully");
      return res.json({
        success: true,
        data: mockCustomer,
      });
    }

    // Tính toán chi tiết công nợ
    const orders = customer.donHangKhachHang || [];

    // Định nghĩa trạng thái đơn hàng
    const confirmedStatuses = [
      "da_xac_nhan",
      "da_dong_goi",
      "da_giao",
      "hoan_thanh",
    ];
    const completedStatuses = ["hoan_thanh"]; // Chỉ đơn hàng đã giao thành công
    const returnedStatuses = ["hoan_hang"]; // Đơn hàng hoàn

    // 1. Công nợ hiện tại (dự tính) - từ đơn hàng đã xác nhận trở lên
    const currentDebt = orders.reduce((sum, order) => {
      // Đơn hàng hoàn = công nợ 0
      if (returnedStatuses.includes(order.trang_thai)) {
        return sum;
      }
      // Chỉ tính đơn hàng đã xác nhận trở lên
      if (confirmedStatuses.includes(order.trang_thai)) {
        return sum + (parseFloat(order.con_phai_tra) || 0);
      }
      return sum;
    }, 0);

    // 2. Công nợ thực tế - chỉ từ đơn hàng đã giao thành công
    const actualDebt = orders.reduce((sum, order) => {
      // Đơn hàng hoàn = công nợ 0
      if (returnedStatuses.includes(order.trang_thai)) {
        return sum;
      }
      // Chỉ tính đơn hàng đã giao thành công
      if (completedStatuses.includes(order.trang_thai)) {
        return sum + (parseFloat(order.con_phai_tra) || 0);
      }
      return sum;
    }, 0);

    // Sử dụng currentDebt làm totalDebt cho backward compatibility
    const totalDebt = currentDebt;

    const totalPurchased = orders.reduce((sum, order) => {
      return sum + (order.tong_tien || 0); // Sử dụng tong_tien (tổng tiền sản phẩm)
    }, 0);

    // Format dữ liệu đơn hàng (sử dụng logic mới)
    const orderData = orders.map((order) => ({
      order_id: order.id,
      order_code: order.ma_don_hang,
      order_date: order.ngay_ban,
      total_amount: order.tong_tien, // Tổng tiền sản phẩm
      cod_amount: order.tien_cod || 0, // Tiền COD
      paid_amount: order.tong_da_tra || 0, // Đã thanh toán
      debt_amount: returnedStatuses.includes(order.trang_thai)
        ? 0 // Đơn hàng hoàn = công nợ 0
        : order.con_phai_tra || 0, // Công nợ (từ con_phai_tra)
      order_status: order.trang_thai, // Trạng thái đơn hàng
      is_returned: returnedStatuses.includes(order.trang_thai), // Đã hoàn hàng
      is_completed: completedStatuses.includes(order.trang_thai), // Đã giao thành công
      payment_status:
        (order.tong_da_tra || 0) >= (order.tien_cod || 0)
          ? "paid" // Đã thanh toán đủ COD
          : (order.tong_da_tra || 0) > 0
          ? "partial" // Thanh toán một phần
          : "unpaid", // Chưa thanh toán
    }));

    // Tạo timeline bao gồm đơn hàng và thanh toán
    const orderTimeline = orders.map((order) => ({
      type: "order",
      title: `Đơn hàng ${order.ma_don_hang}`,
      date: order.ngay_ban,
      description: `Tổng tiền: ${new Intl.NumberFormat("vi-VN", {
        style: "currency",
        currency: "VND",
      }).format(order.tong_tien)} | COD: ${new Intl.NumberFormat("vi-VN", {
        style: "currency",
        currency: "VND",
      }).format(order.tien_cod || 0)} | Công nợ: ${new Intl.NumberFormat(
        "vi-VN",
        {
          style: "currency",
          currency: "VND",
        }
      ).format(order.con_phai_tra || 0)}`,
    }));

    const paymentTimeline = payments.map((payment) => ({
      type: "payment",
      title: payment.type === "thu" ? "Thanh toán" : "Tạo công nợ",
      date: payment.created_at,
      description: `${
        payment.type === "thu" ? "Thu" : "Chi"
      }: ${new Intl.NumberFormat("vi-VN", {
        style: "currency",
        currency: "VND",
      }).format(payment.amount)} - ${payment.note || ""}`,
    }));

    const timeline = [...orderTimeline, ...paymentTimeline];

    const customerDetail = {
      customer_id: customer.id,
      customer_name: customer.ho_ten,
      customer_phone: customer.so_dien_thoai,
      customer_email: customer.email,
      customer_address: customer.dia_chi,
      created_date: customer.createdAt,
      last_order_date:
        orders.length > 0
          ? Math.max(...orders.map((o) => new Date(o.ngay_ban)))
          : null,
      total_debt: totalDebt, // Công nợ hiện tại (dự tính)
      actual_debt: actualDebt, // Công nợ thực tế (đã giao thành công)
      total_purchased: totalPurchased,
      orders: orderData,
      payments: payments.map((payment) => ({
        payment_id: payment.id,
        payment_date: payment.created_at,
        amount: payment.amount,
        payment_method:
          payment.payment_method === "cash"
            ? "Tiền mặt"
            : payment.payment_method === "transfer"
            ? "Chuyển khoản"
            : payment.payment_method === "card"
            ? "Thẻ"
            : payment.payment_method || "Tiền mặt",
        note: payment.note || "",
        type: payment.type === "thu" ? "Thu tiền" : "Chi tiền",
        created_by: payment.created_by || "Hệ thống",
      })),
      timeline: timeline.sort((a, b) => new Date(b.date) - new Date(a.date)),
    };

    console.log("✅ Customer debt detail retrieved successfully");
    res.json({
      success: true,
      data: customerDetail,
    });
  } catch (error) {
    console.error("❌ Error in getCustomerDebtDetail:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy chi tiết công nợ",
      error: error.message,
    });
  }
};

// Tạo phiếu thu/chi
const createPayment = async (req, res) => {
  try {
    const {
      customer_id,
      type,
      amount,
      payment_method,
      note,
      is_partial_payment,
      order_id, // Thêm order_id để cập nhật tong_da_tra
    } = req.body;

    // Convert customer_id to integer if it's a string
    const customerId = parseInt(customer_id);

    console.log("💰 Creating payment:", {
      ...req.body,
      customer_id: customerId,
      customer_id_original: customer_id,
      customer_id_type: typeof customer_id,
    });

    // Validate input
    if (!customerId || isNaN(customerId) || !type || !amount) {
      return res.status(400).json({
        success: false,
        message:
          "Thiếu thông tin bắt buộc: customer_id (phải là số), type, amount",
        received: { customer_id, customerId, type, amount },
      });
    }

    if (amount <= 0) {
      return res.status(400).json({
        success: false,
        message: "Số tiền phải lớn hơn 0",
      });
    }

    if (!["thu", "chi"].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Type phải là "thu" hoặc "chi"',
      });
    }

    // Thực sự cập nhật database
    const {
      NguoiDung,
      CongNoNguoiDung,
      Payment,
      DonHang,
    } = require("../models");

    // Lấy thông tin khách hàng và công nợ hiện tại
    const customer = await NguoiDung.findByPk(customerId, {
      include: [
        {
          model: CongNoNguoiDung,
          as: "congNo",
        },
      ],
    });
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy khách hàng",
      });
    }

    // Lấy hoặc tạo bản ghi công nợ
    let debtRecord = customer.congNo;
    if (!debtRecord) {
      debtRecord = await CongNoNguoiDung.create({
        nguoi_dung_id: customerId,
        tong_cong_no: 0,
      });
    }

    const previousDebt = parseFloat(debtRecord.tong_cong_no || 0);
    const paymentAmount = parseFloat(amount);

    // Tính toán công nợ mới
    let newDebtAmount;
    if (type === "thu") {
      // Thu tiền -> giảm công nợ (cho phép âm nếu khách hàng trả thừa)
      newDebtAmount = previousDebt - paymentAmount;
    } else {
      // Chi tiền -> tăng công nợ (hoàn tiền cho khách hàng)
      newDebtAmount = previousDebt + paymentAmount;
    }

    // Cập nhật công nợ trong database
    await debtRecord.update({
      tong_cong_no: newDebtAmount,
    });

    // Lưu payment vào database
    const savedPayment = await Payment.create({
      customer_id: customerId,
      type,
      amount: paymentAmount,
      payment_method: payment_method || "cash",
      note: note || "",
      created_by: req.user?.username || "test",
      status: "completed",
    });

    // Cập nhật tong_da_tra trong đơn hàng nếu có order_id
    let orderUpdate = null;
    if (order_id && type === "thu") {
      const order = await DonHang.findByPk(order_id);
      if (order) {
        const currentPaid = order.tong_da_tra || 0;
        const newPaidAmount = currentPaid + paymentAmount;

        await order.update({
          tong_da_tra: newPaidAmount,
        });

        orderUpdate = {
          order_id: order_id,
          previous_paid: currentPaid,
          payment_amount: paymentAmount,
          new_paid_amount: newPaidAmount,
        };

        console.log(
          `📦 Updated order ${order_id} tong_da_tra: ${currentPaid} -> ${newPaidAmount}`
        );
      }
    }

    console.log(
      `📊 Updated debt for customer ${customerId}: ${previousDebt} -> ${newDebtAmount} VND`
    );

    // Tạo response với thông tin cập nhật thực tế
    const updatedDebt = {
      customer_id: customerId,
      previous_debt: previousDebt,
      payment_amount: paymentAmount,
      remaining_debt: newDebtAmount,
      payment_date: savedPayment.created_at,
    };

    console.log("✅ Payment created successfully:", savedPayment.toJSON());
    console.log("💰 Debt updated:", updatedDebt);

    res.status(201).json({
      success: true,
      message: (() => {
        if (type === "thu") {
          if (newDebtAmount > 0) {
            return `Thu tiền thành công. Công nợ còn lại: ${new Intl.NumberFormat(
              "vi-VN",
              {
                style: "currency",
                currency: "VND",
              }
            ).format(newDebtAmount)}`;
          } else if (newDebtAmount === 0) {
            return "Thu tiền thành công. Khách hàng đã thanh toán đủ công nợ.";
          } else {
            return `Thu tiền thành công. Khách hàng đã trả thừa ${new Intl.NumberFormat(
              "vi-VN",
              {
                style: "currency",
                currency: "VND",
              }
            ).format(Math.abs(newDebtAmount))}`;
          }
        } else {
          return `Hoàn tiền thành công. Đã hoàn ${new Intl.NumberFormat(
            "vi-VN",
            {
              style: "currency",
              currency: "VND",
            }
          ).format(paymentAmount)} cho khách hàng.`;
        }
      })(),
      data: {
        payment: savedPayment,
        debt_update: updatedDebt,
        order_update: orderUpdate, // Thêm thông tin cập nhật đơn hàng
        customer: {
          id: customer.id,
          name: customer.ho_ten,
          previous_debt: previousDebt,
          current_debt: newDebtAmount,
        },
      },
    });
  } catch (error) {
    console.error("❌ Error in createPayment:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi tạo phiếu thu/chi",
      error: error.message,
    });
  }
};

// Lấy báo cáo công nợ
const getDebtReport = async (req, res) => {
  try {
    const { start_date, end_date, type = "overview" } = req.query;

    console.log("📊 Getting debt report:", req.query);

    // Xây dựng điều kiện thời gian
    let dateCondition = {};
    if (start_date && end_date) {
      dateCondition = {
        ngay_ban: {
          [Op.between]: [new Date(start_date), new Date(end_date)],
        },
      };
    }

    // 1. Tính tổng công nợ hiện tại
    const totalDebtResult = await CongNoNguoiDung.findOne({
      attributes: [
        [sequelize.fn("SUM", sequelize.col("tong_cong_no")), "totalDebt"],
        [sequelize.fn("COUNT", sequelize.col("id")), "totalCustomers"],
      ],
      where: {
        tong_cong_no: { [Op.gt]: 0 },
      },
    });

    const totalDebt = parseFloat(totalDebtResult?.dataValues?.totalDebt || 0);
    const totalCustomers = parseInt(
      totalDebtResult?.dataValues?.totalCustomers || 0
    );

    // 2. Tính tổng thanh toán
    const totalPaymentsResult = await Payment.findOne({
      attributes: [[sequelize.fn("SUM", sequelize.col("amount")), "totalPaid"]],
      where: {
        type: "thu",
        status: "completed",
        ...dateCondition,
      },
    });

    const totalPaid = parseFloat(
      totalPaymentsResult?.dataValues?.totalPaid || 0
    );

    // 3. Tính công nợ quá hạn (giả sử đơn hàng > 30 ngày chưa thanh toán)
    const overdueDate = new Date();
    overdueDate.setDate(overdueDate.getDate() - 30);

    const overdueDebtResult = await DonHang.findOne({
      attributes: [
        [sequelize.fn("SUM", sequelize.col("con_phai_tra")), "overdueDebt"],
      ],
      where: {
        trang_thai: ["da_xac_nhan", "da_dong_goi", "da_giao"],
        con_phai_tra: { [Op.gt]: 0 },
        ngay_ban: { [Op.lt]: overdueDate },
      },
    });

    const overdueDebt = parseFloat(
      overdueDebtResult?.dataValues?.overdueDebt || 0
    );

    // 4. Tính tỷ lệ thu hồi
    const collectionRate =
      totalDebt > 0
        ? Math.round((totalPaid / (totalDebt + totalPaid)) * 100)
        : 0;

    // 5. Trung bình công nợ mỗi khách hàng
    const avgDebtPerCustomer =
      totalCustomers > 0 ? Math.round(totalDebt / totalCustomers) : 0;

    // 6. Dữ liệu biểu đồ theo tháng (6 tháng gần nhất)
    const chartData = [];
    for (let i = 5; i >= 0; i--) {
      const monthStart = new Date();
      monthStart.setMonth(monthStart.getMonth() - i);
      monthStart.setDate(1);
      monthStart.setHours(0, 0, 0, 0);

      const monthEnd = new Date(monthStart);
      monthEnd.setMonth(monthEnd.getMonth() + 1);
      monthEnd.setDate(0);
      monthEnd.setHours(23, 59, 59, 999);

      // Tổng đơn hàng trong tháng
      const monthOrdersResult = await DonHang.findOne({
        attributes: [
          [sequelize.fn("SUM", sequelize.col("tong_phai_tra")), "monthDebt"],
        ],
        where: {
          ngay_ban: { [Op.between]: [monthStart, monthEnd] },
          trang_thai: ["da_xac_nhan", "da_dong_goi", "da_giao", "hoan_thanh"],
        },
      });

      // Tổng thanh toán trong tháng
      const monthPaymentsResult = await Payment.findOne({
        attributes: [
          [sequelize.fn("SUM", sequelize.col("amount")), "monthPaid"],
        ],
        where: {
          created_at: { [Op.between]: [monthStart, monthEnd] },
          type: "thu",
          status: "completed",
        },
      });

      const monthDebt = parseFloat(
        monthOrdersResult?.dataValues?.monthDebt || 0
      );
      const monthPaid = parseFloat(
        monthPaymentsResult?.dataValues?.monthPaid || 0
      );

      chartData.push({
        month: `T${monthStart.getMonth() + 1}`,
        debt: monthDebt,
        paid: monthPaid,
      });
    }

    // 7. Dữ liệu pie chart
    const totalOrders = totalDebt + totalPaid;
    const pieData = [
      {
        name: "Đã thanh toán",
        value:
          totalOrders > 0 ? Math.round((totalPaid / totalOrders) * 100) : 0,
        color: "#52c41a",
      },
      {
        name: "Chưa thanh toán",
        value:
          totalOrders > 0
            ? Math.round(((totalDebt - overdueDebt) / totalOrders) * 100)
            : 0,
        color: "#ff4d4f",
      },
      {
        name: "Quá hạn",
        value:
          totalOrders > 0 ? Math.round((overdueDebt / totalOrders) * 100) : 0,
        color: "#ff7875",
      },
    ];

    // 8. Top khách hàng nợ nhiều nhất
    const topDebtors = await CongNoNguoiDung.findAll({
      attributes: [
        "nguoi_dung_id",
        "tong_cong_no",
        [sequelize.col("nguoiDung.ho_ten"), "customer_name"],
      ],
      include: [
        {
          model: NguoiDung,
          as: "nguoiDung",
          attributes: ["ho_ten"],
        },
      ],
      where: {
        tong_cong_no: { [Op.gt]: 0 },
      },
      order: [["tong_cong_no", "DESC"]],
      limit: 5,
    });

    // Lấy số đơn hàng cho mỗi khách hàng
    const topDebtorsWithOrders = await Promise.all(
      topDebtors.map(async (debtor, index) => {
        const orderCount = await DonHang.count({
          where: {
            khach_hang_id: debtor.nguoi_dung_id,
            trang_thai: ["da_xac_nhan", "da_dong_goi", "da_giao", "hoan_thanh"],
          },
        });

        return {
          rank: index + 1,
          customer: debtor.dataValues.customer_name || "N/A",
          debt: parseFloat(debtor.tong_cong_no),
          orders: orderCount,
        };
      })
    );

    const reportData = {
      stats: {
        totalDebt: Math.round(totalDebt),
        overdueDebt: Math.round(overdueDebt),
        collectionRate,
        avgDebtPerCustomer,
      },
      chartData,
      pieData,
      topDebtors: topDebtorsWithOrders,
    };

    console.log("✅ Debt report retrieved successfully from database");
    res.json({
      success: true,
      data: reportData,
    });
  } catch (error) {
    console.error("❌ Error in getDebtReport:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy báo cáo công nợ",
      error: error.message,
    });
  }
};

// Tạo ghi chú nội bộ
const createInternalNote = async (req, res) => {
  try {
    const { customer_id, content, type } = req.body;

    console.log("📝 Creating internal note:", req.body);

    // Validate input
    if (!customer_id || !content || !type) {
      return res.status(400).json({
        success: false,
        message: "Thiếu thông tin bắt buộc",
      });
    }

    // Kiểm tra khách hàng có tồn tại không
    const customer = await NguoiDung.findByPk(customer_id);
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy khách hàng",
      });
    }

    // Tạo ghi chú nội bộ
    const { InternalNote } = require("../models");
    const note = await InternalNote.create({
      customer_id,
      content,
      type,
      created_by: req.user?.username || "test",
    });

    console.log("✅ Internal note created successfully");
    res.status(201).json({
      success: true,
      message: "Tạo ghi chú nội bộ thành công",
      data: note,
    });
  } catch (error) {
    console.error("❌ Error in createInternalNote:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi tạo ghi chú nội bộ",
      error: error.message,
    });
  }
};

// Lấy danh sách ghi chú nội bộ
const getInternalNotes = async (req, res) => {
  try {
    const { customerId } = req.params;

    console.log(`📝 Getting internal notes for customer: ${customerId}`);

    // Kiểm tra khách hàng có tồn tại không
    const customer = await NguoiDung.findByPk(customerId);
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy khách hàng",
      });
    }

    // Lấy ghi chú nội bộ từ database
    const { InternalNote } = require("../models");
    const notes = await InternalNote.findAll({
      where: { customer_id: customerId },
      order: [["created_at", "DESC"]],
      limit: 50, // Giới hạn 50 ghi chú gần nhất
    });

    console.log(
      `✅ Found ${notes.length} internal notes for customer ${customerId}`
    );
    res.json({
      success: true,
      data: notes,
    });
  } catch (error) {
    console.error("❌ Error in getInternalNotes:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy ghi chú nội bộ",
      error: error.message,
    });
  }
};

/**
 * Đồng bộ hóa công nợ từ đơn hàng
 */
const syncDebtFromOrders = async (req, res) => {
  try {
    console.log("🔄 Syncing debt from orders using con_phai_tra...");
    console.log("🔐 User:", req.user?.username || "No user info");

    // Lấy tất cả đơn hàng có khách hàng
    const orders = await DonHang.findAll({
      where: {
        khach_hang_id: { [Op.not]: null },
      },
      attributes: [
        "id",
        "khach_hang_id",
        "con_phai_tra",
        "ma_don_hang",
        "trang_thai",
      ],
    });

    console.log(`📊 Found ${orders.length} orders with customers`);

    // Tính tổng công nợ theo khách hàng từ con_phai_tra
    const customerDebts = {};
    orders.forEach((order) => {
      const customerId = order.khach_hang_id;
      const remainingAmount = parseFloat(order.con_phai_tra) || 0;

      if (!customerDebts[customerId]) {
        customerDebts[customerId] = 0;
      }
      customerDebts[customerId] += remainingAmount;
    });

    console.log(`👥 Processing ${Object.keys(customerDebts).length} customers`);

    // Cập nhật bảng công nợ
    const results = [];
    for (const customerId in customerDebts) {
      const totalDebt = customerDebts[customerId];

      // Tìm hoặc tạo bản ghi công nợ
      let debtRecord = await CongNoNguoiDung.findOne({
        where: { nguoi_dung_id: customerId },
      });

      if (!debtRecord) {
        debtRecord = await CongNoNguoiDung.create({
          nguoi_dung_id: customerId,
          tong_cong_no: totalDebt,
          ghi_chu: "Đồng bộ từ con_phai_tra",
        });
        console.log(
          `✅ Created debt record for customer ${customerId}: ${totalDebt} VND`
        );
      } else {
        const oldDebt = debtRecord.tong_cong_no;
        await debtRecord.update({
          tong_cong_no: totalDebt,
        });
        console.log(
          `✅ Updated debt record for customer ${customerId}: ${oldDebt} -> ${totalDebt} VND`
        );
      }

      results.push({
        customer_id: customerId,
        debt_from_orders: totalDebt,
        updated_debt: totalDebt,
      });
    }

    res.json({
      success: true,
      message: "Đồng bộ hóa công nợ thành công",
      data: {
        synced_customers: results.length,
        results: results,
      },
    });
  } catch (error) {
    console.error("❌ Error syncing debt:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

/**
 * Test simple payment endpoint
 */
const testSimplePayment = (req, res) => {
  console.log("✅ Simple payment test received:", req.body);
  res.json({
    success: true,
    message: "Test payment endpoint working",
    received_data: req.body,
  });
};

module.exports = {
  getDebtList,
  getCustomerDebtDetail,
  createPayment,
  getDebtReport,
  createInternalNote,
  getInternalNotes,
  syncDebtFromOrders,
  testSimplePayment,
};
